import React from "react";
import { Box, Typography, useTheme } from "@mui/material";

interface ChatMessageProps {
  text: string;
  timestamp: Date;
  isOwnMessage: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  text,
  timestamp,
  isOwnMessage,
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        backgroundColor: isOwnMessage
          ? theme.palette.mode === "dark"
            ? "rgba(255,255,255,0.1)"
            : "#64748B"
          : theme.palette.background.paper,
        p: 2,
        borderRadius: "16px",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 2px 8px rgba(0,0,0,0.2)"
            : "0 2px 8px rgba(0,0,0,0.04)",
        maxWidth: "80%",
        alignSelf: isOwnMessage ? "flex-end" : "flex-start",
      }}
    >
      <Typography
        sx={{
          color: isOwnMessage ? "#fff" : "text.primary",
          mb: 1,
          wordBreak: "break-word",
        }}
      >
        {text}
      </Typography>
      <Typography
        variant="caption"
        sx={{
          color: isOwnMessage ? "rgba(255,255,255,0.8)" : "text.secondary",
          display: "block",
          textAlign: isOwnMessage ? "right" : "left",
        }}
      >
        {timestamp.getHours().toString().padStart(2, "0")}:
        {timestamp.getMinutes().toString().padStart(2, "0")}
      </Typography>
    </Box>
  );
};
