# backend/p1.py

import requests
import google.generativeai as genai
from flask import Blueprint, json, jsonify, request
from auth import require_permission

p1 = Blueprint('/p1', __name__)

GOOGLE_API_KEY = "AIzaSyARlEfomkKKioDlZCKddMAz590HenzSDrI"

genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel('gemini-1.5-flash')

@p1.route('/get-attributes', methods=['POST'])
@require_permission('p1')
def get_attributes():
    product_name = request.json['productName']

    return jsonify(attributes=get_product_attributes(product_name))

@p1.route('/search-images', methods=['POST'])
@require_permission('p1')
def search_images():
    product_name = request.json['productName']
    product_attributes = request.json['productAttributes']
    return jsonify(images=search_google_images(product_name,product_attributes))


@p1.route('/get-product-details', methods=['POST'])
@require_permission('p1')
def get_product_details():
    product_name = request.json['productName']
    return jsonify(get_attributes_in_one_query(product_name))


def get_product_attributes(product_name):
    prompt = f"""
    Lütfen aşağıda belirtilen ürün için gerekli tüm öznitelikleri listele. Kullanıcı, bu öznitelikleri ürün tanımlama modülünde dolduracak. Ürün adı kullanıcı tarafından girilecek ve senin görevin, bu ürünle ilgili olabilecek tüm temel ve ilave öznitelikleri sunmak. İşte ürün bilgileri:

    Ürün Adı: {product_name}

    Lütfen bu ürün için aşağıdaki bilgileri dikkate alarak öznitelikleri listele en fazla 20 tane öznitelik ver: 
    Bana sadece öznitelikleri virgülle ayrılmış biçimde geri dönder. Öznitelik adlarını virgülle ayrılmış biçimde istiyorum. Örnek olarak "Renk, Boyut, Marka" gibi.
    """
    response = model.generate_content(prompt)
    attribute_list = [attribute.strip() for attribute in response.text.split(",")]
    return attribute_list

def search_google_images(product_name,product_attributes):
    image_search_name = generate_image_keyword(product_name,product_attributes)
    search_engine_id = "620171eb47fac4a86"  # Google Custom Search Engine ID
    url = f"https://www.googleapis.com/customsearch/v1?q={image_search_name}&cx={search_engine_id}&searchType=image&key={GOOGLE_API_KEY}"
    response = requests.get(url)
    results = response.json().get('items', [])
    images = [item['link'] for item in results[:3]]  # İlk 3 resmi al
    return images

def get_attributes_in_one_query(product_name):
    # İlk prompt ile öznitelikleri al
    attributes = get_product_attributes(product_name)
    
    # Ana prompt
    prompt = f"""
        Dil modeli, ürün tanımlama ve öznitelik belirleme konusunda uzmandır. Aşağıdaki ürün için tüm ilgili öznitelikleri ve her bir öznitelik için en uygun örnekleri listele. Kullanıcının amacı, ürünün detaylı bir tanımını yapmak için bu öznitelikleri ve örnekleri ürün tanımlama modülüne eklemektir.

        Ürün adı: {product_name}
        Ürün öznitelikleri: {attributes}
        Aşağıdaki öznitelikler hariç tüm öznitelikler ve örnekler listelenecektir: "Stok Miktarı", "İlan Tarihi", "İlan Sahibi", "İletişim Bilgileri", "Ürün Adı". 
        Bu ürün ve özniteliklerinin her özniteliği için, o özniteliğe ait uygun örnekleri sun. Tahmini fiyat bilgisini de ekle. Öznitelikleri, örnekleri ve tahmini fiyat bilgisini aşağıdaki JSON formatında geri döndür:

        {{
            "attributes": {{
                "Öznitelik1": ["Örnek1", "Örnek2", "Örnek3"],
                "Öznitelik2": ["Örnek1", "Örnek2", "Örnek3"]
            }},
            "tahmini fiyat": "Örnek Fiyat"
        }}

        Lütfen sadece öznitelik adlarını ve örnekleri JSON formatında döndür.
    """
    
    response = model.generate_content(
        prompt,
        generation_config=genai.GenerationConfig(response_mime_type="application/json")
    )

    # Yanıtı JSON formatında işleme
    try:
        json_response = json.loads(response.text)

        # Kusursuz tanımlanmış ürün adı almak için ikinci bir prompt oluştur
        product_name_prompt = f"""
            Ürün adı: {product_name}
            Bu ürünü daha iyi tanımlayan bir ad önerin. Arama sonuçlarında doğru görsellerin çıkması için kusursuz bir ürün adı üretin.
            Örneğin ürün adı makas ise Terzi Makası olarak düzenleyin. Ayakkabı ise Yürüyüş Ayakkabısı olarak düzenleyin. Bisiklet ise Dağ Bisikleti gibi Bunlara benzer dönüşümler yapın. 
            Örnek Girdiler için örnek çıktılar:
            Ürün adı: "Bisiklet" -> Kusursuz Ürün Adı: "Dağ Bisikleti"
            Ürün adı: "Ayakkabı" -> Kusursuz Ürün Adı: "Yürüyüş Ayakkabısı"
            Ürün adı: "Makas" -> Kusursuz Ürün Adı: "Terzi Makası"
            Ürün adı: "Kalem" -> Kusursuz Ürün Adı: "Dolma Kalem"
            Ürün adı: "Bilgisayar" -> Kusursuz Ürün Adı: "Dizüstü Bilgisayar"
            Ürün adı: "Telefon" -> Kusursuz Ürün Adı: "Akıllı Telefon"
            
            Lütfen aşağıdaki formatın dışına çıkma yalnızca JSON formatında, kusursuz ürün adını geri döndür:
 
            {{
                "kusursuz_urun_adi": "Tanımlanmış Ürün Adı"
            }}
        """

        # AI'den kusursuz tanımlanmış ürün adını almak
        product_name_response = model.generate_content(
            product_name_prompt,
            generation_config=genai.GenerationConfig(response_mime_type="application/json")
        )

        # Yanıtı işleme
        product_name_json = json.loads(product_name_response.text)
        kusursuz_product_name = product_name_json.get("kusursuz_urun_adi", product_name)
        #burası düzeltildi
        # Görselleri almak için API çağrısı
        images_response = search_google_images(kusursuz_product_name, attributes)

        # Sonuçları birleştir
        result = {
            'urun-adı': product_name,
            'kusursuz_urun_adi': kusursuz_product_name,
            'images': images_response
        }
        result.update(json_response)
        return result
    except json.JSONDecodeError:
        # Eğer JSON formatı değilse hata döndür
        return {"error": "Yanıt JSON formatında değil.", "response": response.text}

import random

def generate_image_keyword(product_name, product_attributes):
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")

    prompt = f"""
    You are an intelligent assistant that helps generate effective image search keywords.

    Based on the following product name and its attributes, generate **10 different short and specific keywords** (each 2 to 5 words) in **English** that best describe this product visually for a Google Image Search.

    Product Name: "{product_name}"
    Product Attributes: {', '.join(product_attributes)}

    ⚠️ Output the 10 keywords as a plain numbered list, one per line. Do not add any explanations or formatting.
    """

    response = model.generate_content(
        prompt,
        generation_config=genai.types.GenerationConfig(
            temperature=1.0,
            top_p=0.95,
            top_k=40,
            max_output_tokens=200
        )
    )

    # Satırları ayır, boşlukları temizle
    keywords = [line.strip("- ").strip() for line in response.text.split("\n") if line.strip()]
    keywords = [kw for kw in keywords if kw and len(kw.split()) <= 6]  # Çok uzun olanları filtrele

    # Rastgele bir tane seç ve döndür
    return random.choice(keywords)



