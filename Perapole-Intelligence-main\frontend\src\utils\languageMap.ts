// utils/language.ts

// Harita: Ülke kodu -> dil adı (desteklenen dil isimleriyle eşleşecek şekilde)
export const countryToLanguageMap: { [key: string]: string } = {
    TR: "turkish",
    US: "english",
    GB: "english",
    DE: "german",
    FR: "french",
    ES: "spanish",
    IT: "italian",
    GR: "greek",
    PT: "portuguese",
    NL: "dutch",
    BE: "dutch",
    CH: "german",
    AT: "german",
    SE: "swedish",
    NO: "norwegian",
    DK: "danish",
    FI: "finnish",
    PL: "polish",
    HU: "hungarian",
    CZ: "czech",
    SK: "slovak",
    RO: "romanian",
    BG: "bulgarian",
    HR: "croatian",
    RS: "serbian",
    UA: "ukrainian",
    RU: "russian",
    CN: "chinese (simplified)",
    TW: "chinese (traditional)",
    HK: "chinese (traditional)",
    JP: "japanese",
    KR: "korean",
    IN: "hindi",
    PK: "urdu",
    BD: "bengali",
    VN: "vietnamese",
    TH: "thai",
    MY: "malay",
    ID: "indonesian",
    KZ: "kazakh",
    IR: "persian",
    IL: "hebrew",
    SA: "arabic",
    AE: "arabic",
    EG: "arabic",
    DZ: "arabic",
    MA: "arabic",
    ZA: "english",
    NG: "english",
    KE: "swahili",
    ET: "amharic",
    GH: "english",
    AU: "english",
    NZ: "english",
    SG: "english",
    PH: "filipino",
  };
  
  // Harita: Dil adı (küçük harf) -> dil kodu
  export const languageNameToCodeMap: { [key: string]: string } = {
    "turkish": "tr",
    "english": "en",
    "german": "de",
    "french": "fr",
    "spanish": "es",
    "italian": "it",
    "greek": "el",
    "portuguese": "pt",
    "dutch": "nl",
    "swedish": "sv",
    "norwegian": "no",
    "danish": "da",
    "finnish": "fi",
    "polish": "pl",
    "hungarian": "hu",
    "czech": "cs",
    "slovak": "sk",
    "romanian": "ro",
    "bulgarian": "bg",
    "croatian": "hr",
    "serbian": "sr",
    "ukrainian": "uk",
    "russian": "ru",
    "chinese (simplified)": "zh-CN",
    "chinese (traditional)": "zh-TW",
    "japanese": "ja",
    "korean": "ko",
    "hindi": "hi",
    "urdu": "ur",
    "bengali": "bn",
    "vietnamese": "vi",
    "thai": "th",
    "malay": "ms",
    "indonesian": "id",
    "kazakh": "kk",
    "persian": "fa",
    "hebrew": "iw",
    "arabic": "ar",
    "amharic": "am",
    "swahili": "sw",
    "filipino": "tl",
  };
  
  // Fonksiyon: Ülke koduna göre dil adını döndür
  export function getLanguageByCountryCode(code: string): string {
    return countryToLanguageMap[code.toUpperCase()] || "english";
  }
  
  // Fonksiyon: Ülke kodundan dil kodu elde et (örn. "TR" -> "tr")
  export function getLanguageCodeByCountry(code: string): string {
    const language = getLanguageByCountryCode(code);
    return languageNameToCodeMap[language.toLowerCase()] || "en";
  }
  