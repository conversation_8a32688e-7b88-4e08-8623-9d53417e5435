import i18n from "@i18n";
import { FuseNavItemType } from "@fuse/core/FuseNavigation/types/FuseNavItemType";
import ar from "./navigation-i18n/ar";
import en from "./navigation-i18n/en";
import tr from "./navigation-i18n/tr";

i18n.addResourceBundle("en", "navigation", en);
i18n.addResourceBundle("tr", "navigation", tr);
i18n.addResourceBundle("ar", "navigation", ar);

//NOT: Navigation yapabilmek için linkler burada tanımlanıyor.

const navigationConfig: FuseNavItemType[] = [
  {
    id: "p1-component",
    title: "P1",
    translate: "P1",
    type: "item",
    icon: "heroicons-outline:star",
    url: "p1",
  },
  {
    id: "p2-component",
    title: "P2",
    translate: "P2",
    type: "item",
    icon: "heroicons-outline:star",
    url: "p2",
  },
  {
    id: "p3-component",
    title: "P3",
    translate: "P3",
    type: "item",
    icon: "heroicons-outline:star",
    url: "p3",
  },
  {
    id: "p4-component",
    title: "P4",
    translate: "P4",
    type: "item",
    icon: "heroicons-outline:star",
    url: "p4",
  },

];

export default navigationConfig;
