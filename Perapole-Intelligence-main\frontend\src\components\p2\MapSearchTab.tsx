"use client";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { Box, Button, Typography, TextField, MenuItem } from "@mui/material";
import { GoogleMap, useLoadScript, Circle } from "@react-google-maps/api";
import { useSearchParams } from "next/navigation";
import axios from "axios";
import ResultsTable from "@/components/p2/ResultsTable";
import FuseLoading from "@fuse/core/FuseLoading";
import debounce from "lodash/debounce";

const libraries: ("places")[] = ["places"];

const mapContainerStyle = {
  width: "100%",
  height: "600px",
};

const defaultCenter = {
  lat: 38.9637,
  lng: 35.2433,
};

const defaultRadius = 50000;

const dummyProducts = [
  "Elektronik Ürünler",
  "Beyaz Eşya",
  "Mobilya",
  "Tekstil",
  "Otomotiv Parçaları",
];

const MapSearchTab = () => {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!,
    libraries,
  });

  const searchParams = useSearchParams();
  const initialKeyword = decodeURIComponent(searchParams.get("product") || "");

  const [center, setCenter] = useState(defaultCenter);
  const [radius, setRadius] = useState(defaultRadius);
  const [selectedProduct, setSelectedProduct] = useState("");
  const [disableProductSelect, setDisableProductSelect] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(false);

  const circleRef = useRef<google.maps.Circle | null>(null);

  const debouncedSetCenter = useCallback(
    debounce((newCenter: { lat: number; lng: number }) => {
      setCenter(newCenter);
    }, 100),
    []
  );

  const debouncedSetRadius = useCallback(
    debounce((newRadius: number) => {
      setRadius(newRadius);
    }, 100),
    []
  );

  useEffect(() => {
    if (initialKeyword) {
      setSelectedProduct(initialKeyword);
      setDisableProductSelect(true);
    }
  }, [initialKeyword]);

  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      setCenter({
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      });
    }
  }, []);

  const handleCircleLoad = (circle: google.maps.Circle) => {
    circleRef.current = circle;
  };

  const onCircleCenterChanged = () => {
    if (circleRef.current) {
      const newCenter = circleRef.current.getCenter();
      if (newCenter) {
        const newLat = newCenter.lat();
        const newLng = newCenter.lng();
        if (newLat !== center.lat || newLng !== center.lng) {
          debouncedSetCenter({ lat: newLat, lng: newLng });
        }
      }
    }
  };

  const onCircleRadiusChanged = () => {
    if (circleRef.current) {
      const newRadius = circleRef.current.getRadius();
      if (newRadius && newRadius !== radius) {
        debouncedSetRadius(newRadius);
      }
    }
  };

  const getCountryCodeByLatLon = async (lat: number, lng: number) => {
    try {
      const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
      const response = await axios.get(geocodeUrl);
      const results = response.data.results;
      if (results.length > 0) {
        const countryComponent = results[0].address_components.find((comp: any) =>
          comp.types.includes("country")
        );
        return countryComponent ? countryComponent.short_name : "UNKNOWN";
      }
    } catch (error) {
      console.error("Ülke kodu bulunamadı:", error);
    }
    return "UNKNOWN";
  };

  const handleSearch = async () => {
    console.log("Arama Başladı:");
    console.log("Seçilen Ürün:", selectedProduct);
    console.log("Merkez:", center);
    console.log("Yarıçap (metre):", radius);

    setLoading(true);
    setShowResults(false);

    try {
      const countryCode = await getCountryCodeByLatLon(center.lat, center.lng);
      console.log("Bulunan Ülke:", countryCode);

      const payload = {
        centerLatitude: center.lat,
        centerLongitude: center.lng,
        radius: radius,
        searchText: selectedProduct,
        country: countryCode,
      };

      const token = localStorage.getItem("jwt_token");
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/p2/get-suppliers-by-map`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      console.log("Arama Sonuçları:", response.data);
      setCompanies(response.data);
      setShowResults(true);
    } catch (error) {
      console.error("Harita Araması Hatası:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loadError) return <div>Harita yüklenemedi</div>;
  if (!isLoaded) return <div>Harita yükleniyor...</div>;

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Harita Üzerinden Çember Seçimi
      </Typography>

      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={center}
        zoom={6}
        onClick={handleMapClick}
        options={{
          gestureHandling: "cooperative",
          zoomControl: true,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
        }}
      >
        <Circle
          center={center}
          radius={radius}
          options={{
            fillColor: "lightblue",
            fillOpacity: 0.2,
            strokeColor: "blue",
            strokeOpacity: 0.8,
            strokeWeight: 2,
            draggable: true,
            editable: true,
            clickable: true,
            zIndex: 1,
          }}
          onLoad={handleCircleLoad}
          onCenterChanged={onCircleCenterChanged}
          onRadiusChanged={onCircleRadiusChanged}
        />
      </GoogleMap>

      <Box sx={{
        mt: 3,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        gap: 2,
        flexWrap: "wrap",
      }}>
        <TextField
          select
          label="Ürün Kategorisi"
          value={selectedProduct}
          onChange={(e) => setSelectedProduct(e.target.value)}
          size="small"
          sx={{ minWidth: 250 }}
          disabled={disableProductSelect}
        >
          <MenuItem value="">
            <em>Ürün Seçin</em>
          </MenuItem>
          {disableProductSelect && selectedProduct && (
            <MenuItem value={selectedProduct}>{selectedProduct}</MenuItem>
          )}
          {dummyProducts.map((product) => (
            <MenuItem key={product} value={product}>
              {product}
            </MenuItem>
          ))}
        </TextField>

        <Button
          variant="contained"
          color="primary"
          onClick={handleSearch}
          disabled={!selectedProduct}
        >
          Ara
        </Button>
      </Box>

      {loading && (
        <Box sx={{
          mt: 4,
          width: "100%",
          height: "300px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}>
          <FuseLoading />
        </Box>
      )}

      {!loading && showResults && companies.length > 0 && (
        <Box sx={{ mt: 4 }}>
          <ResultsTable companies={companies} />
        </Box>
      )}

      {!loading && showResults && companies.length === 0 && (
        <Box sx={{ mt: 4, textAlign: "center" }}>
          <Typography variant="body1" color="text.secondary">
            Arama kriterlerinize uygun sonuç bulunamadı.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MapSearchTab;
