"use client";
import React from "react";
import {
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  useTheme,
  alpha,
} from "@mui/material";
import { useRouter } from "next/navigation";
import { getLanguageByCountryCode } from "@/utils/languageMap";

interface Company {
  name: string;
  address: string;
  phone: string;
  website?: string;
  district: string;
  city: string;
  country: string;
  category: string;
}

interface ResultsTableProps {
  companies: Company[];
}

const ResultsTable: React.FC<ResultsTableProps> = ({ companies }) => {
  const theme = useTheme();
  const router = useRouter();

  const handleContactClick = (company: Company) => {
    if (!company.country) {
      console.warn("❗ Şirketin 'country' alanı eksik:", company);
      return;
    }

    const supplierLang = getLanguageByCountryCode(company.country);
    const targetUrl = `/p3?customerLang=turkish&supplierLang=${supplierLang.toLowerCase()}`;

    console.log("🔁 Yönlendirme yapılıyor:", targetUrl);
    router.push(targetUrl);
  };

  return (
    <Card
      sx={{
        borderRadius: "24px",
        background:
          theme.palette.mode === "dark"
            ? "rgba(0,0,0,0.6)"
            : "rgba(255,255,255,0.9)",
        backdropFilter: "blur(10px)",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 4px 24px rgba(0,0,0,0.2)"
            : "0 4px 24px rgba(0,0,0,0.06)",
        overflow: "hidden",
      }}
    >
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow
              sx={{
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? theme.palette.background.default
                    : "#F8FAFC",
              }}
            >
              <TableCell sx={{ fontWeight: 600, color: "text.primary", py: 3 }}>
                İsim
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: "text.primary", py: 3 }}>
                Adres
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: "text.primary", py: 3 }}>
                Telefon
              </TableCell>
              <TableCell sx={{ fontWeight: 600, color: "text.primary", py: 3 }}>
                Web Sitesi
              </TableCell>
              <TableCell align="right" sx={{ fontWeight: 600, color: "text.primary", py: 3 }}>
                İletişim
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {companies.length > 0 ? (
              companies.map((company, index) => (
                <TableRow
                  key={index}
                  sx={{
                    "&:last-child td, &:last-child th": { border: 0 },
                    "&:hover": {
                      backgroundColor:
                        theme.palette.mode === "dark"
                          ? "rgba(255,255,255,0.05)"
                          : "#F8FAFC",
                    },
                  }}
                >
                  <TableCell sx={{ py: 3, fontWeight: 500 }}>
                    {company.name}
                  </TableCell>
                  <TableCell sx={{ py: 3 }}>{company.address}</TableCell>
                  <TableCell sx={{ py: 3 }}>{company.phone}</TableCell>
                  <TableCell sx={{ py: 3 }}>
                    {company.website || "-"}
                  </TableCell>
                  <TableCell align="right" sx={{ py: 3 }}>
                    <Button
                      variant="outlined"
                      onClick={() => handleContactClick(company)}
                      sx={{
                        color: "text.secondary",
                        borderColor: theme.palette.divider,
                        "&:hover": {
                          borderColor: "text.secondary",
                          backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                        },
                        textTransform: "none",
                        borderRadius: "12px",
                        px: 3,
                      }}
                    >
                      İletişim
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} sx={{ textAlign: "center", py: 4 }}>
                  Arama kriterlerinize uygun sonuç bulunamadı.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Card>
  );
};

export default ResultsTable;
