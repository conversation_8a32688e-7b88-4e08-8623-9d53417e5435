import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import _ from 'lodash';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import { Alert } from '@mui/material';
import useNavigate from '@fuse/hooks/useNavigate';

const NEXT_PUBLIC_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

const schema = z.object({
	username: z.string().nonempty('Lütfen kullanıcı adını girin.'),
	password: z.string().nonempty('Lütfen şifreyi girin.')
});

type FormType = {
	username: string;
	password: string;
};

const defaultValues: FormType = {
	username: '',
	password: ''
};

function AuthJsCredentialsSignInForm() {
	const navigate = useNavigate();
	const { control, formState, handleSubmit, setError } = useForm<FormType>({
		mode: 'onChange',
		defaultValues,
		resolver: zodResolver(schema)
	});

	const { isValid, dirtyFields, errors } = formState;

	async function onSubmit(formData: FormType) {
		const { username, password } = formData;

		try {
			// username:password -> Base64 encode
			const encodedCredentials = btoa(`${username}:${password}`);

			const response = await fetch(`${NEXT_PUBLIC_BASE_URL}/auth/login`, {
				method: 'POST',
				headers: {
					'Authorization': `Basic ${encodedCredentials}`
				}
			});

			const data = await response.json();

			if (response.ok) {
				localStorage.setItem('jwt_token', data.token);
				navigate('/p1');
				return true;
			} else {
				setError('root', {
					type: 'manual',
					message: data.message || 'Geçersiz kullanıcı adı veya şifre!'
				});
				return false;
			}
		} catch (error) {
			setError('root', {
				type: 'manual',
				message: 'Bir hata oluştu. Lütfen tekrar deneyin.'
			});
			return false;
		}
	}

	return (
		<form
			name="loginForm"
			noValidate
			className="mt-32 flex w-full flex-col justify-center"
			onSubmit={handleSubmit(onSubmit)}
		>
			{errors?.root?.message && (
				<Alert
					className="mb-32"
					severity="error"
					sx={(theme) => ({
						backgroundColor: theme.palette.error.light,
						color: theme.palette.error.dark
					})}
				>
					{errors?.root?.message}
				</Alert>
			)}

			<Controller
				name="username"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-24"
						label="Kullanıcı Adı"
						error={!!errors.username}
						helperText={errors?.username?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Controller
				name="password"
				control={control}
				render={({ field }) => (
					<TextField
						{...field}
						className="mb-24"
						label="Şifre"
						type="password"
						error={!!errors.password}
						helperText={errors?.password?.message}
						variant="outlined"
						required
						fullWidth
					/>
				)}
			/>

			<Button
				variant="contained"
				color="secondary"
				className="mt-16 w-full"
				aria-label="Sign in"
				disabled={_.isEmpty(dirtyFields) || !isValid}
				type="submit"
				size="large"
			>
				Giriş Yap
			</Button>
		</form>
	);
}

export default AuthJsCredentialsSignInForm;
