"use client";
import React, { useEffect, useState, useRef } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Grid,
  Divider,
  Paper,
  useTheme,
  ImageList,
  ImageListItem,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import ImageOutlinedIcon from "@mui/icons-material/ImageOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import CircularProgress from "@mui/material/CircularProgress";
import { useRouter } from "next/navigation";
import axios from "axios";

const NEXT_PUBLIC_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
const DEFAULT_IMAGE_COUNT = 3;

interface ProductCardProps {
  onBack: () => void;
  productData: {
    title: string;
    attributes: Record<string, string[]>;
    images?: string[];
    estimatedPrice?: string;
  };
}

const ProductCard: React.FC<ProductCardProps> = ({ onBack, productData }) => {
  const router = useRouter();
  const theme = useTheme();
  const [images, setImages] = useState<string[]>(() => productData.images?.slice(0, DEFAULT_IMAGE_COUNT) || []);
  const [loadingIndex, setLoadingIndex] = useState<number | null>(null);
  const fileInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Her zaman 3 kutu dolu olacak şekilde başlat
  useEffect(() => {
    let imgs = productData.images?.slice(0, DEFAULT_IMAGE_COUNT) || [];
    if (imgs.length < DEFAULT_IMAGE_COUNT) {
      imgs = imgs.concat(Array(DEFAULT_IMAGE_COUNT - imgs.length).fill(""));
    }
    setImages(imgs);
  }, [productData.images]);

  // Her görsel için yükleme işlemi
  const handleImageUpload = (index: number) => async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoadingIndex(index);
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result as string;
      const updatedImages = [...images];
      updatedImages[index] = base64String;
      setImages(updatedImages);
      setLoadingIndex(null);
    };
    reader.readAsDataURL(file);
  };

  // Silme işlemi: her zaman search images ile yeni bir görsel getir
  const handleImageDelete = async (indexToDelete: number) => {
    try {
      setLoadingIndex(indexToDelete);
      const token = localStorage.getItem("jwt_token");
      const payload = { productName: productData.title, productAttributes: productData.attributes };
      const response = await axios.post(`${NEXT_PUBLIC_BASE_URL}/p1/search-images`, payload, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const newImages = response.data.images || [];
      const updatedImages = [...images];
      updatedImages[indexToDelete] = newImages[0] || "";
      setImages(updatedImages);
    } catch (error) {
      console.error("Yeni görsel alınırken hata oluştu:", error);
    } finally {
      setLoadingIndex(null);
    }
  };

  return (
    <Grid container spacing={4}>
      <Grid item xs={12} md={8}>
        <Card
          sx={{
            borderRadius: "24px",
            background:
              theme.palette.mode === "dark"
                ? "rgba(0,0,0,0.6)"
                : "rgba(255,255,255,0.9)",
            backdropFilter: "blur(10px)",
            boxShadow:
              theme.palette.mode === "dark"
                ? "0 4px 24px rgba(0,0,0,0.2)"
                : "0 4px 24px rgba(0,0,0,0.06)",
            overflow: "visible",
            position: "relative",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 4,
              gap: 3,
              position: "relative",
              "&::after": {
                content: '""',
                position: "absolute",
                bottom: -16,
                left: 0,
                right: 0,
                height: "1px",
                background:
                  theme.palette.mode === "dark"
                    ? `linear-gradient(90deg, ${theme.palette.divider} 0%, rgba(255,255,255,0) 100%)`
                    : "linear-gradient(90deg, #E2E8F0 0%, rgba(226, 232, 240, 0) 100%)",
              },
            }}
          >
            <IconButton
              onClick={onBack}
              sx={{
                color: "text.secondary",
                backgroundColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255,255,255,0.05)"
                    : "rgba(100, 116, 139, 0.1)",
                "&:hover": {
                  backgroundColor:
                    theme.palette.mode === "dark"
                      ? "rgba(255,255,255,0.1)"
                      : "rgba(100, 116, 139, 0.2)",
                },
              }}
            >
              <ArrowBackIcon />
            </IconButton>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 700, color: "text.primary", mb: 0.5 }}>
                Oluşturulan Ürün Kartı
              </Typography>
              <Typography variant="body2" sx={{ color: "text.secondary" }}>
                Seçilen özelliklerle oluşturulan ürün kartı önizlemesi
              </Typography>
            </Box>
          </Box>

          <CardContent sx={{ p: 4, pt: 5 }}>
            <Typography variant="h4" sx={{ fontWeight: 800, color: "text.primary", mb: 3 }}>
              {productData.title}
            </Typography>

            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
                <ImageOutlinedIcon sx={{ color: "text.secondary" }} />
                <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                  Ürün Görselleri
                </Typography>
              </Box>
              <ImageList
                sx={{ width: "100%", height: 240, borderRadius: "12px", overflow: "hidden" }}
                cols={3}
                rowHeight={240}
              >
                {images.map((img, index) => (
                  <ImageListItem key={index} sx={{ position: "relative", display: "flex", flexDirection: "column", alignItems: "center", height: 240, justifyContent: "space-between" }}>
                    <Box sx={{ width: "100%", height: 140, display: "flex", alignItems: "center", justifyContent: "center", overflow: "hidden", position: "relative" }}>
                      {img ? (
                        <img
                          src={img}
                          alt={`${productData.title} - ${index + 1}`}
                          loading="lazy"
                          style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }}
                        />
                      ) : (
                        <Box sx={{ width: "100%", height: "100%", background: theme.palette.background.default }} />
                      )}
                      <IconButton
                        onClick={() => handleImageDelete(index)}
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          backgroundColor: "rgba(255,255,255,0.8)",
                          "&:hover": { backgroundColor: "rgba(255,0,0,0.7)" },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                    <Box sx={{ width: "100%", textAlign: "center", mt: 1 }}>
                      <input
                        type="file"
                        accept="image/*"
                        style={{ display: "none" }}
                        ref={el => { fileInputRefs.current[index] = el; }}
                        onChange={handleImageUpload(index)}
                      />
                      <button
                        style={{
                          border: "none",
                          background: "none",
                          cursor: "pointer",
                          color: theme.palette.text.secondary,
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          margin: "0 auto",
                        }}
                        onClick={() => fileInputRefs.current[index]?.click()}
                        disabled={loadingIndex === index}
                      >
                        {loadingIndex === index ? (
                          <CircularProgress size={20} />
                        ) : (
                          <>
                            <CloudUploadIcon sx={{ fontSize: 24 }} />
                            <span style={{ fontSize: 12 }}>Kendi Fotoğrafınızı Yükleyin</span>
                          </>
                        )}
                      </button>
                    </Box>
                  </ImageListItem>
                ))}
              </ImageList>
            </Box>

            <Grid container spacing={3}>
              {Object.entries(productData.attributes).map(([category, values]) => (
                <Grid item xs={12} sm={6} key={category}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2.5,
                      borderRadius: "16px",
                      backgroundColor:
                        theme.palette.mode === "dark"
                          ? "rgba(255,255,255,0.05)"
                          : "#F8FAFC",
                      border: `1px solid ${theme.palette.divider}`,
                    }}
                  >
                    <Box sx={{ display: "flex", alignItems: "center", mb: 2, gap: 1 }}>
                      <CheckCircleIcon sx={{ color: "text.secondary", fontSize: 20 }} />
                      <Typography variant="subtitle1" sx={{ fontWeight: 600, color: "text.primary" }}>
                        {category}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                      {values.map((value, index) => (
                        <Chip
                          key={index}
                          label={value}
                          sx={{
                            backgroundColor: theme.palette.background.paper,
                            border: `1px solid ${theme.palette.divider}`,
                            color: "text.primary",
                            borderRadius: "8px",
                            "& .MuiChip-label": {
                              px: 2,
                              py: 1,
                              fontWeight: 500,
                            },
                          }}
                        />
                      ))}
                    </Box>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={4}>
        <Card
          sx={{
            borderRadius: "24px",
            background: theme.palette.background.paper,
            boxShadow:
              theme.palette.mode === "dark"
                ? "0 4px 24px rgba(0,0,0,0.2)"
                : "0 4px 24px rgba(0,0,0,0.06)",
            overflow: "hidden",
            position: "relative",
            height: "100%",
            minHeight: 300,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <CardContent sx={{ p: 4, height: "100%", position: "relative" }}>
            <Box sx={{ position: "relative", zIndex: 1 }}>
              <Typography variant="h6" sx={{ color: "text.primary", mb: 3, fontWeight: 600 }}>
                Ürün Özeti
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" sx={{ color: "text.secondary", mb: 1 }}>
                  Seçilen Özellik Sayısı
                </Typography>
                <Typography variant="h4" sx={{ color: "text.primary", fontWeight: 700 }}>
                  {Object.keys(productData.attributes).length}
                </Typography>
              </Box>

              <Divider sx={{ borderColor: theme.palette.divider, my: 3 }} />

              <Box sx={{ display: "flex", alignItems: "center", gap: 1, mb: 2 }}>
                <InfoOutlinedIcon sx={{ color: "text.secondary" }} />
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  Özellik Detayları
                </Typography>
              </Box>

              {Object.entries(productData.attributes).map(([category, values], index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: "text.secondary", mb: 0.5 }}>
                    {category}
                  </Typography>
                  <Typography variant="body1" sx={{ color: "text.primary", fontWeight: 500 }}>
                    {values.join(", ")}
                  </Typography>
                </Box>
              ))}
            </Box>

            <Box
              sx={{
                position: "absolute",
                top: -50,
                right: -50,
                width: 200,
                height: 200,
                borderRadius: "50%",
                background:
                  theme.palette.mode === "dark"
                    ? "rgba(255,255,255,0.05)"
                    : "#F8FAFC",
              }}
            />
            <Box
              sx={{
                position: "absolute",
                bottom: -30,
                left: -30,
                width: 120,
                height: 120,
                borderRadius: "50%",
                background:
                  theme.palette.mode === "dark"
                    ? "rgba(255,255,255,0.05)"
                    : "#F8FAFC",
              }}
            />
            <Box sx={{ mt: 4, textAlign: "center", zIndex: 1, position: "relative" }}>
              <button
                style={{
                  padding: "12px 24px",
                  backgroundColor: "#0D9488",
                  color: "white",
                  border: "none",
                  borderRadius: "8px",
                  fontWeight: "600",
                  fontSize: "1rem",
                  cursor: "pointer",
                  transition: "all 0.3s ease",
                }}
                onClick={async () => {
                  const payload = {
                    productTitle: productData.title,
                    attributes: productData.attributes,
                  };

                  try {
                    const token = localStorage.getItem("jwt_token");
                    const response = await axios.post(
                      `${NEXT_PUBLIC_BASE_URL}/p2/generate-keyword`,
                      payload,
                      {
                        headers: {
                          Authorization: `Bearer ${token}`,
                        },
                      }
                    );

                    const keyword = response.data.keyword;
                    router.push(`/p2?product=${encodeURIComponent(keyword)}`);
                  } catch (error) {
                    console.error("Anahtar kelime alınırken hata oluştu:", error);
                  }
                }}
              >
                Bu Ürün İçin Tedarikçi Ara
              </button>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default ProductCard;
