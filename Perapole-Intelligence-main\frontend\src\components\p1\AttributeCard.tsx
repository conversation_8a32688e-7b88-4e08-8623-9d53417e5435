import React, { useState } from "react";
import {
  Card,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  Box,
  Typography,
  TextField,
  Chip,
  Checkbox,
  useTheme,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ClearIcon from "@mui/icons-material/Clear";

interface AttributeCardProps {
  attrName: string;
  values: string[];
  selectedValues: string[];
  onDelete: (attrName: string) => void;
  onChange: (attrName: string, values: string[]) => void;
  onValueDelete: (attrName: string, value: string) => void;
}

const AttributeCard: React.FC<AttributeCardProps> = ({
  attrName,
  values,
  selectedValues,
  onDelete,
  onChange,
  onValueDelete,
}) => {
  const theme = useTheme();
  const [newValue, setNewValue] = useState("");

  const handleAddNewValue = () => {
    if (!newValue.trim()) return;

    if (values.includes(newValue.trim())) {
      setNewValue("");
      return;
    }

    const newValues = [...values, newValue.trim()];
    onChange(attrName, [...selectedValues, newValue.trim()]);
    setNewValue("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddNewValue();
    }
  };

  const handleChipDelete = (e: React.MouseEvent, value: string) => {
    e.preventDefault();
    e.stopPropagation();
    onValueDelete(attrName, value);
  };

  return (
    <Card
      sx={{
        p: 3,
        height: "100%",
        borderRadius: "24px",
        background:
          theme.palette.mode === "dark"
            ? "rgba(0,0,0,0.6)"
            : "rgba(255,255,255,0.9)",
        backdropFilter: "blur(10px)",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 4px 24px rgba(0,0,0,0.2)"
            : "0 4px 24px rgba(0,0,0,0.06)",
        transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "4px",
          background: theme.palette.text.secondary,
          opacity: 0,
          transition: "opacity 0.3s ease",
        },
        "&:hover": {
          transform: "translateY(-8px)",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 12px 40px rgba(0,0,0,0.3)"
              : "0 12px 40px rgba(0,0,0,0.12)",
          "&::before": {
            opacity: 1,
          },
        },
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={3}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            color: "text.primary",
            fontSize: "1.1rem",
            letterSpacing: "-0.3px",
          }}
        >
          {attrName}
        </Typography>
        <IconButton
          size="small"
          onClick={() => onDelete(attrName)}
          sx={{
            color: "text.secondary",
            "&:hover": {
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              color: theme.palette.error.main,
            },
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </Box>

      <FormControl fullWidth sx={{ mb: 2 }}>
        <Select
          multiple
          value={selectedValues}
          onChange={(e) => onChange(attrName, e.target.value as string[])}
          displayEmpty
          renderValue={(selected) => {
            if (!(selected as string[]).length) {
              return <em>Seçiniz</em>;
            }
            return (
              <Box
                sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, py: 0.5 }}
                onClick={(e) => e.stopPropagation()}
              >
                {(selected as string[]).map((value) => (
                  <Chip
                    key={value}
                    label={value}
                    onDelete={(e) => handleChipDelete(e as any, value)}
                    onClick={(e) => e.stopPropagation()}
                    deleteIcon={
                      <ClearIcon onMouseDown={(e) => e.stopPropagation()} />
                    }
                    size="small"
                    sx={{
                      backgroundColor:
                        theme.palette.mode === "dark"
                          ? "rgba(255,255,255,0.05)"
                          : "#F8FAFC",
                      color: "text.primary",
                      borderRadius: "8px",
                      height: "auto",
                      border: `1px solid ${theme.palette.divider}`,
                      "& .MuiChip-label": {
                        display: "block",
                        py: 0.5,
                        lineHeight: "1.2",
                      },
                      "& .MuiChip-deleteIcon": {
                        color: "text.secondary",
                        "&:hover": {
                          color: theme.palette.text.primary,
                        },
                      },
                    }}
                  />
                ))}
              </Box>
            );
          }}
          sx={{
            borderRadius: "16px",
            backgroundColor: theme.palette.background.paper,
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor:
                theme.palette.mode === "dark"
                  ? "rgba(255,255,255,0.1)"
                  : "#E2E8F0",
              borderRadius: "16px",
            },
            "&:hover .MuiOutlinedInput-notchedOutline": {
              borderColor: theme.palette.text.secondary,
            },
            "& .MuiSelect-select": {
              borderRadius: "16px",
              backgroundColor: theme.palette.background.paper,
            },
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                maxHeight: 300,
                borderRadius: "12px",
                mt: 1,
                backgroundColor: theme.palette.background.paper,
                "& .MuiMenuItem-root": {
                  px: 2,
                  py: 1,
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  "&:hover": {
                    backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                  },
                  "&.Mui-selected": {
                    backgroundColor: alpha(theme.palette.text.secondary, 0.15),
                    "&:hover": {
                      backgroundColor: alpha(theme.palette.text.secondary, 0.2),
                    },
                  },
                },
              },
            },
          }}
        >
          {values.map((value) => (
            <MenuItem key={value} value={value}>
              <Checkbox
                checked={selectedValues.includes(value)}
                sx={{
                  color: theme.palette.text.secondary,
                  "&.Mui-checked": {
                    color: theme.palette.text.secondary,
                  },
                }}
              />
              {value}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        <TextField
          size="small"
          placeholder="Yeni değer ekle"
          value={newValue}
          onChange={(e) => setNewValue(e.target.value)}
          onKeyPress={handleKeyPress}
          sx={{
            flex: 1,
            maxWidth: "70%",
            "& .MuiOutlinedInput-root": {
              borderRadius: "12px",
              backgroundColor: theme.palette.background.paper,
              "& fieldset": {
                borderColor: theme.palette.divider,
              },
              "&:hover fieldset": {
                borderColor: theme.palette.text.secondary,
              },
            },
          }}
        />
        <IconButton
          onClick={handleAddNewValue}
          sx={{
            color: "text.secondary",
            "&:hover": {
              backgroundColor: alpha(theme.palette.text.secondary, 0.1),
            },
          }}
        >
          <AddCircleOutlineIcon />
        </IconButton>
      </Box>
    </Card>
  );
};

export default AttributeCard;
