"use client";
import React, { useState } from "react";
import { Box, Container, Typography, Card, useTheme } from "@mui/material";
import { ChatInput } from "@/components/p3/ChatInput";
import axios from "axios";

interface Message {
  id: number;
  text: string;
  timestamp: Date;
  isBot: boolean;
  thinking?: boolean;
}

const P4Container = () => {
  const theme = useTheme();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "Merhaba! Ben PeraPole Intelligence (Pi). Size PeraPole platformu ve ürünleri hakkında nasıl yardımcı olabilirim?",
      timestamp: new Date(),
      isBot: true,
    },
  ]);
  const [userMessage, setUserMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSendMessage = async () => {
    if (userMessage.trim()) {
      const userMsgId = Date.now();

      setMessages((prev) => [
        ...prev,
        {
          id: userMsgId,
          text: userMessage,
          timestamp: new Date(),
          isBot: false,
        },
      ]);

      setMessages((prev) => [
        ...prev,
        {
          id: userMsgId + 1,
          text: "...",
          timestamp: new Date(),
          isBot: true,
          thinking: true,
        },
      ]);

      setLoading(true);
      try {
        const token = localStorage.getItem("jwt_token");

        const response = await axios.post(
          `${process.env.NEXT_PUBLIC_BASE_URL}/p4/chatbot-query`,
          {
            message: userMessage,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setMessages((prev) => [
          ...prev.filter((msg) => !msg.thinking),
          {
            id: userMsgId + 2,
            text: response.data.response,
            timestamp: new Date(),
            isBot: true,
          },
        ]);
      } catch (error: any) {
        console.error("Error getting response:", error);

        let errorMsg = "Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin.";
        if (error.response?.status === 401) {
          errorMsg = "🔐 Oturum süreniz doldu. Lütfen tekrar giriş yapın.";
        } else if (error.response?.status === 403) {
          errorMsg = "⛔ Bu kısma erişim yetkiniz yok. Lütfen sistem yöneticinizle iletişime geçin.";
        } else {
          errorMsg = "❌ Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.";
        }
        setMessages((prev) => [
          ...prev.filter((msg) => !msg.thinking),
          {
            id: userMsgId + 2,
            text: errorMsg,
            timestamp: new Date(),
            isBot: true,
          },
        ]);
      } finally {
        setLoading(false);
      }

      setUserMessage("");
    }
  };


  return (
    <Box
      sx={{
        p: { xs: 1.5, md: 2.5 },
        minHeight: "85vh",
        background:
          theme.palette.mode === "dark"
            ? `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.paper} 100%)`
            : "linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Container
        maxWidth="md"
        sx={{ flex: 1, display: "flex", flexDirection: "column" }}
      >
        <Box sx={{ mb: 2.5 }}>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, color: "text.primary", mb: 0.5 }}
          >
            AI Asistan
          </Typography>
          <Typography variant="body2" sx={{ color: "text.secondary" }}>
            PeraPole platformu hakkında sorularınızı yanıtlamak için buradayım
          </Typography>
        </Box>

        <Card
          sx={{
            flex: 1,
            borderRadius: "24px",
            background:
              theme.palette.mode === "dark"
                ? "rgba(0,0,0,0.6)"
                : "rgba(255,255,255,0.9)",
            backdropFilter: "blur(10px)",
            boxShadow:
              theme.palette.mode === "dark"
                ? "0 4px 24px rgba(0,0,0,0.2)"
                : "0 4px 24px rgba(0,0,0,0.06)",
            display: "flex",
            flexDirection: "column",
            overflow: "hidden",
            position: "relative",
          }}
        >
          <Box
            sx={{
              p: 2,
              flexGrow: 1,
              backgroundColor:
                theme.palette.mode === "dark"
                  ? theme.palette.background.default
                  : "#F8FAFC",
              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              gap: 2,
            }}
          >
            {messages.map((msg) => (
              <Box
                key={msg.id}
                sx={{
                  backgroundColor: msg.isBot
                    ? theme.palette.background.paper
                    : theme.palette.mode === "dark"
                      ? "rgba(255,255,255,0.1)"
                      : "#64748B",
                  p: 1.5,
                  borderRadius: "12px",
                  boxShadow:
                    theme.palette.mode === "dark"
                      ? "0 2px 8px rgba(0,0,0,0.2)"
                      : "0 2px 8px rgba(0,0,0,0.04)",
                  maxWidth: "80%",
                  alignSelf: msg.isBot ? "flex-start" : "flex-end",
                  opacity: msg.thinking ? 0.7 : 1,
                }}
              >
                <Typography
                  sx={{
                    color: msg.isBot ? "text.primary" : "#fff",
                    mb: 1,
                    wordBreak: "break-word",
                  }}
                >
                  {msg.text}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: msg.isBot
                      ? "text.secondary"
                      : "rgba(255,255,255,0.8)",
                    display: "block",
                    textAlign: msg.isBot ? "left" : "right",
                  }}
                >
                  {msg.timestamp.getHours().toString().padStart(2, "0")}:
                  {msg.timestamp.getMinutes().toString().padStart(2, "0")}
                </Typography>
              </Box>
            ))}
          </Box>

          <ChatInput
            value={userMessage}
            onChange={setUserMessage}
            onSend={handleSendMessage}
          />
        </Card>
      </Container>
    </Box>
  );
};

export default P4Container;
