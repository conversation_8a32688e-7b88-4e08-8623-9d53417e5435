import React from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
  Typography,
  Grid,
  Chip,
  useTheme,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface StatDetail {
  id: number;
  type: {
    code: string;
    name: string;
    stat_group: string | null;
  };
  value: any;
}

interface PlayerStats {
  season: string;
  league?: string;
  team?: string;
  matches?: number;
  goals?: number;
  assists?: number;
  details?: StatDetail[];
}

interface SeasonAccordionProps {
  season: PlayerStats;
}

export const SeasonAccordion: React.FC<SeasonAccordionProps> = ({ season }) => {
  const theme = useTheme();

  // Helper function to find stat value by code
  const getStatValue = (code: string) => {
    const stat = season.details?.find(detail => detail.type.code === code);
    if (!stat) return null;
    
    if (typeof stat.value === 'object') {
      if ('total' in stat.value) return stat.value.total;
      if ('average' in stat.value) return stat.value.average;
    }
    return stat.value;
  };

  const StatCard = ({
    value,
    label,
  }: {
    value: string | number;
    label: string;
  }) => (
    <Box>
      <Typography variant="h6" sx={{ fontWeight: 700, color: "text.primary" }}>
        {value}
      </Typography>
      <Typography variant="body2" sx={{ color: "text.secondary" }}>
        {label}
      </Typography>
    </Box>
  );

  const StatItem = ({
    label,
    value,
  }: {
    label: string;
    value: string | number | null;
  }) => (
    <Grid item xs={6} md={3}>
      <Typography variant="body2" sx={{ color: "text.secondary" }}>
        {label}
      </Typography>
      <Typography variant="body1" sx={{ color: "text.primary" }}>
        {value ?? '-'}
      </Typography>
    </Grid>
  );

  const StatSection = ({
    title,
    children,
  }: {
    title: string;
    children: React.ReactNode;
  }) => (
    <Box sx={{ mb: 3 }}>
      <Typography
        variant="subtitle1"
        sx={{ fontWeight: 600, mb: 2, color: "text.primary" }}
      >
        {title}
      </Typography>
      <Grid container spacing={2}>
        {children}
      </Grid>
    </Box>
  );

  // Get main stats
  const appearances = getStatValue('appearances');
  const minutesPlayed = getStatValue('minutes-played');
  const rating = getStatValue('rating');
  const shotsTotal = getStatValue('shots-total');
  const shotsOnTarget = getStatValue('shots-on-target');
  const passAccuracy = getStatValue('accurate-passes-percentage');
  const keyPasses = getStatValue('key-passes');
  const tackles = getStatValue('tackles');
  const interceptions = getStatValue('interceptions');
  const duelsWon = getStatValue('duels-won');
  const totalDuels = getStatValue('total-duels');
  const dispossessed = getStatValue('dispossessed');
  const teamWins = getStatValue('team-wins');
  const teamDraws = getStatValue('team-draws');
  const teamLost = getStatValue('team-lost');
  const cleanSheets = season.details?.find(detail => detail.type.code === 'cleansheets')?.value?.total;

  return (
    <Accordion
      sx={{
        mb: 1,
        borderRadius: "12px !important",
        "&:before": { display: "none" },
        backgroundColor: "background.paper",
        boxShadow: theme.shadows[2],
      }}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          "& .MuiAccordionSummary-expandIconWrapper": {
            color: "text.secondary",
          },
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Typography sx={{ fontWeight: 600, color: "text.primary" }}>
            {season.season}
          </Typography>
          {season.league && (
            <Chip
              label={season.league}
              size="small"
              sx={{
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark"
                    ? "rgba(255, 255, 255, 0.05)"
                    : "action.hover",
                color: "text.primary",
              }}
            />
          )}
          {season.team && (
            <Typography variant="body2" sx={{ color: "text.secondary" }}>
              {season.team}
            </Typography>
          )}
        </Box>
      </AccordionSummary>

      <AccordionDetails>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Ana İstatistikler */}
          <Box sx={{ display: "flex", gap: 4, mb: 2 }}>
            {appearances && <StatCard value={appearances} label="Maç" />}
            {minutesPlayed && (
              <StatCard value={minutesPlayed} label="Dakika" />
            )}
            {rating && typeof rating === 'object' && rating.average && (
              <StatCard value={rating.average.toFixed(2)} label="Ortalama Puan" />
            )}
          </Box>

          {/* Performans İstatistikleri */}
          <StatSection title="Performans">
            <StatItem label="Maç Sayısı" value={appearances} />
            <StatItem label="İlk 11" value={getStatValue('lineups')} />
            <StatItem label="Yedek" value={getStatValue('bench')} />
            {rating && typeof rating === 'object' && (
              <StatItem label="En Yüksek Puan" value={rating.highest?.toFixed(2)} />
            )}
          </StatSection>

          {/* Hücum İstatistikleri */}
          <StatSection title="Hücum">
            <StatItem label="Toplam Şut" value={shotsTotal} />
            <StatItem label="İsabetli Şut" value={shotsOnTarget} />
            <StatItem label="Pas Başarı %" value={passAccuracy?.toFixed(1)} />
            <StatItem label="Kilit Pas" value={keyPasses} />
          </StatSection>

          {/* Defans İstatistikleri */}
          <StatSection title="Defans">
            <StatItem label="Müdahale" value={tackles} />
            <StatItem label="Top Çalma" value={interceptions} />
            <StatItem 
              label="İkili Mücadele (K/T)" 
              value={duelsWon && totalDuels ? `${duelsWon}/${totalDuels}` : null} 
            />
            <StatItem label="Top Kaybı" value={dispossessed} />
          </StatSection>

          {/* Takım İstatistikleri */}
          <StatSection title="Takım">
            <StatItem label="Galibiyet" value={teamWins} />
            <StatItem label="Beraberlik" value={teamDraws} />
            <StatItem label="Mağlubiyet" value={teamLost} />
            <StatItem label="Clean Sheet" value={cleanSheets} />
          </StatSection>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default SeasonAccordion;