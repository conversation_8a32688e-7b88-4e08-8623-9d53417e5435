# backend/p4.py

from flask import Blueprint, request, jsonify
import google.generativeai as genai
import os
import time
import requests
import tempfile
from auth import require_permission

# Yeni blueprint'i tanımlayın
p4 = Blueprint('p4', __name__)

GOOGLE_API_KEY = "AIzaSyARlEfomkKKioDlZCKddMAz590HenzSDrI"
genai.configure(api_key=GOOGLE_API_KEY)

def upload_to_gemini(path, mime_type=None):
    """Uploads the given file to Gemini. Supports both local paths and online URLs."""
    # Eğer path bir URL ise dosyayı indir ve geçici olarak kaydet
    if path.startswith("http://") or path.startswith("https://"):
        response = requests.get(path)
        response.raise_for_status()  # Dosya indirilemediyse hata verir
        
        # Geçici bir dosya oluştur ve veriyi bu dosyaya yaz
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
            tmp_file.write(response.content)
            tmp_file_path = tmp_file.name
        
        # Geçici dosya yolunu kullanarak dosyayı yükle
        file = genai.upload_file(tmp_file_path, mime_type=mime_type)
        
        # Geçici dosyayı yükleme sonrası sil
        os.remove(tmp_file_path)
    else:
        # Yerel dosya yolunu doğrudan yükle
        file = genai.upload_file(path, mime_type=mime_type)

    print(f"Uploaded file '{file.display_name}' as: {file.uri}")
    return file

def wait_for_files_active(files):
    """Waits for the given files to be active."""
    for file in files:
        while file.state.name == "PROCESSING":
            print(f"Waiting for {file.display_name} to be ready...")
            time.sleep(10)
            file = genai.get_file(file.name)
        if file.state.name != "ACTIVE":
            raise Exception(f"File {file.name} failed to process")
    print("All files are ready.")

def upload_local_file(file_name, mime_type=None):
    """Proje klasöründen bir dosyayı yükle."""
    file_path = os.path.join(os.path.join(os.path.dirname(os.path.abspath(__file__)), "data"), file_name)  # Dosyanın tam yolu
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Dosya bulunamadı: {file_path}")
    
    # Dosyayı yükle
    file = genai.upload_file(file_path, mime_type=mime_type)
    print(f"Uploaded file '{file.display_name}' as: {file.uri}")
    return file

# Modeli başlat, bu da sadece bir kere çalışacak
model = genai.GenerativeModel(
    model_name="gemini-1.5-flash",
    # bu parametreler modelin nasıl çalışacağını belirler
    # temperature : 
    generation_config={
        "temperature": 0.2,
        "top_p": 0.65,
        "top_k": 34,
        "max_output_tokens": 8192,
        "response_mime_type": "text/plain",
    },
    system_instruction="""
        Merhaba, sen PeraPole Intelligence (Pi) adında özel bir chatbot modelisin. Görevin, PeraPole platformu ve ürünleri hakkında kullanıcılara doğru ve detaylı bilgi sağlamak. 
        Aşağıdaki kuralları takip edeceksin:

        1. Sana sağlanan iki dosyayı kullan: "PeraPole Platformu Kullanım Kılavuzu" ve "PeraPole Ürün Soru-Cevap Dosyası". Bu dosyalardan elde edilen bilgileri kullanarak soruları cevapla.
        2. Eğer bir soru, bu dosyalarda bulunmayan bir bilgi içeriyorsa, kullanıcılara bu bilginin mevcut olmadığını açık bir şekilde belirt.
        3. PeraPole ile ilgisi olmayan sorulara yanıt vermeyeceğini nazikçe ifade et. Görevin sadece PeraPole ile ilgili sorularla sınırlıdır.
        4. Amacın, kullanıcıların PeraPole platformunu ve ürünlerini daha iyi anlamalarına yardımcı olmaktır. Net, profesyonel ve kullanıcı dostu yanıtlar vermeye özen göster.
    """
)
# İlk başta dosyalar bir kere yüklensin
files = [
    upload_local_file("user_guide.pdf", mime_type="application/pdf"),
    upload_local_file("faq.pdf", mime_type="application/pdf"),
]

# Dosyaların işlenmesini bekle
wait_for_files_active(files)

# Sohbet oturumunu başlat, bu da sadece bir kere yapılacak
chat_session = model.start_chat(
    history=[
        {
            "role": "user",
            "parts": [
                files[0],
                files[1],
                "Sen PeraPole Intelligence (Pi) adında özel bir chatbot modelisin. Görevin, PeraPole platformu ve ürünleri hakkında kullanıcılara doğru ve detaylı bilgi sağlamak. PeraPole Kılavuzu ve Ürünle ilgili Soru-Cevap dosyalarını incelediniz, şimdi sorulara yanıt verin. Kılavuz ve Soru-Cevap dosyalarında yeterli bilgi yoksa bilginin içerik dosyasında olmadığını yazın. PeraPole hakkında olmayan sorulara cevap vermeyeceğimi belirtin.",
            ],
        },
    ]
)

@p4.route('/chatbot-query', methods=['POST'])
@require_permission('p4')
def chatbot_query():
    data = request.get_json()
    user_message = data.get("message")
    user_message = "Sen PeraPole Intelligence (Pi) adında özel bir chatbot modelisin. Görevin, PeraPole platformu ve ürünleri hakkında kullanıcılara doğru ve detaylı bilgi sağlamak. PeraPole Kılavuzu ve Ürünle ilgili Soru-Cevap dosyalarını inceledin, şimdi sorulara yanıt ver. Kılavuz ve Soru-Cevap dosyalarında yeterli bilgi yoksa bilginin içerik dosyasında olmadığını yaz. PeraPole hakkında olmayan sorulara cevap vermeyeceğini belirt. Sana gelen mesaj şu şekilde: " + user_message 
    # Mevcut sohbet oturumu üzerinden mesaj gönder ve yanıt al
    response = chat_session.send_message(user_message)
    return jsonify({"response": response.text})