export const API_BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || '/';

type FetchOptions = RequestInit;

export class FetchApiError extends Error {
	status: number;
	data: unknown;

	constructor(status: number, data: unknown) {
		super(`FetchApiError: ${status}`);
		this.status = status;
		this.data = data;
	}
}

// Token getter
const getAuthHeader = (): Record<string, string> => {
	const token = typeof window !== "undefined" ? localStorage.getItem("jwt_token") : null;
	return token ? { Authorization: `Bearer ${token}` } : {};
};

// Global headers configuration
export const globalHeaders: Record<string, string> = {};

// Update global headers manually (opsiyonel)
export const setGlobalHeaders = (newHeaders: Record<string, string>) => {
	Object.assign(globalHeaders, newHeaders);
};

// Remove specific global headers
export const removeGlobalHeaders = (headerKeys: string[]) => {
	headerKeys.forEach((key) => {
		delete globalHeaders[key];
	});
};

// Main fetch wrapper
const apiFetch = async (endpoint: string, options: FetchOptions = {}) => {
	const { headers, ...restOptions } = options;
	const method = restOptions.method || 'GET';

	const config: FetchOptions = {
		headers: {
			...(method !== 'GET' && { 'Content-Type': 'application/json' }),
			...getAuthHeader(),        // 🔐 JWT token otomatik olarak burada eklenir
			...globalHeaders,          // manuel eklenen global header'lar
			...headers                 // çağrıya özel header'lar
		},
		...restOptions
	};

	try {
		const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

		if (!response.ok) {
			const errorData = await response.json().catch(() => null);
			throw new FetchApiError(response.status, errorData);
		}

		return response;
	} catch (error) {
		console.error('Error in apiFetch:', error);
		throw error;
	}
};

export default apiFetch;
