import React from "react";
import {
  Box,
  FormControl,
  Select,
  MenuItem,
  Card,
  Typography,
  useTheme,
} from "@mui/material";
import { alpha } from "@mui/material/styles";
import LocationOnOutlinedIcon from "@mui/icons-material/LocationOnOutlined";
import CategoryOutlinedIcon from "@mui/icons-material/CategoryOutlined";
import SearchIcon from "@mui/icons-material/Search";
import CustomButton from "../p1/CustomButton";

interface SearchFormProps {
  countries: { value: string; label: string }[];
  cities: { value: string; label: string }[];
  districts: { value: string; label: string }[];
  selectedCountry: string;
  selectedCity: string;
  selectedDistrict: string;
  selectedProduct: string;
  dummyProducts: string[];
  onCountryChange: (value: string) => void;
  onCityChange: (value: string) => void;
  onDistrictChange: (value: string) => void;
  onProductChange: (value: string) => void;
  onSearch: () => void;
  disableProductSelect?: boolean;
}

const SearchForm: React.FC<SearchFormProps> = ({
  countries,
  cities,
  districts,
  selectedCountry,
  selectedCity,
  selectedDistrict,
  selectedProduct,
  dummyProducts,
  onCountryChange,
  onCityChange,
  onDistrictChange,
  onProductChange,
  onSearch,
  disableProductSelect = false,
}) => {
  const theme = useTheme();
  const SelectStyles = {
    borderRadius: "16px",
    backgroundColor: theme.palette.background.paper,
    "& .MuiOutlinedInput-notchedOutline": {
      borderColor:
        theme.palette.mode === "dark"
          ? "rgba(255,255,255,0.1)"
          : "rgba(0,0,0,0.08)",
      borderRadius: "16px",
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderColor: "text.secondary",
    },
    "& .MuiSelect-select": {
      borderRadius: "16px",
      backgroundColor: theme.palette.background.paper,
    },
    "& .MuiMenuItem-root": {
      "&:hover": {
        backgroundColor: alpha(theme.palette.text.secondary, 0.1),
      },
      "&.Mui-selected": {
        backgroundColor: alpha(theme.palette.text.secondary, 0.15),
        "&:hover": {
          backgroundColor: alpha(theme.palette.text.secondary, 0.2),
        },
      },
    },
  };

  return (
    <Card
      sx={{
        p: 4,
        borderRadius: "24px",
        background:
          theme.palette.mode === "dark"
            ? "rgba(0,0,0,0.6)"
            : "rgba(255,255,255,0.9)",
        backdropFilter: "blur(10px)",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 4px 24px rgba(0,0,0,0.2)"
            : "0 4px 24px rgba(0,0,0,0.06)",
        mb: 4,
      }}
    >
      <Box
        sx={{
          display: "flex",
          gap: 3,
          flexWrap: { xs: "wrap", md: "nowrap" },
          mb: 3,
        }}
      >
        <Box sx={{ flex: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1.5,
              display: "flex",
              alignItems: "center",
              gap: 1,
              color: "text.secondary",
              fontWeight: 600,
            }}
          >
            <LocationOnOutlinedIcon fontSize="small" />
            Konum Bilgisi
          </Typography>
          <Box sx={{ display: "flex", gap: 2 }}>
            <FormControl fullWidth>
              <Select
                value={selectedCountry}
                onChange={(e) => onCountryChange(e.target.value)}
                displayEmpty
                sx={SelectStyles}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: 300,
                      borderRadius: "12px",
                      mt: 1,
                      boxShadow: "0 4px 24px rgba(0,0,0,0.06)",
                    },
                  },
                }}
              >
                <MenuItem value="">
                  <em>Ülke Seçin</em>
                </MenuItem>
                {countries.map((country) => (
                  <MenuItem key={country.value} value={country.label}>
                    {country.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <Select
                value={selectedCity}
                onChange={(e) => onCityChange(e.target.value)}
                displayEmpty
                disabled={!selectedCountry}
                sx={SelectStyles}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: 300,
                      borderRadius: "12px",
                      mt: 1,
                      boxShadow: "0 4px 24px rgba(0,0,0,0.06)",
                    },
                  },
                }}
              >
                <MenuItem value="">
                  <em>İl/Eyalet Seçin</em>
                </MenuItem>
                {cities.map((city) => (
                  <MenuItem key={city.value} value={city.value}>
                    {city.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <Select
                value={selectedDistrict}
                onChange={(e) => onDistrictChange(e.target.value)}
                displayEmpty
                disabled={!selectedCity}
                sx={SelectStyles}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: 300,
                      borderRadius: "12px",
                      mt: 1,
                      boxShadow: "0 4px 24px rgba(0,0,0,0.06)",
                    },
                  },
                }}
              >
                <MenuItem value="">
                  <em>İlçe/Şehir Seçin</em>
                </MenuItem>
                {districts.map((district) => (
                  <MenuItem key={district.value} value={district.value}>
                    {district.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        <Box sx={{ flex: 1 }}>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1.5,
              display: "flex",
              alignItems: "center",
              gap: 1,
              color: "text.secondary",
              fontWeight: 600,
            }}
          >
            <CategoryOutlinedIcon fontSize="small" />
            Ürün Kategorisi
          </Typography>
          <FormControl fullWidth>
            <Select
              value={selectedProduct}
              onChange={(e) => onProductChange(e.target.value)}
              displayEmpty
              disabled={disableProductSelect}
              sx={SelectStyles}
              MenuProps={{
                PaperProps: {
                  sx: {
                    maxHeight: 300,
                    borderRadius: "12px",
                    mt: 1,
                    boxShadow: "0 4px 24px rgba(0,0,0,0.06)",
                  },
                },
              }}
            >
              <MenuItem value="">
                <em>Ürün Seçin</em>
              </MenuItem>
              {disableProductSelect && selectedProduct && (
              <MenuItem value={selectedProduct}>{selectedProduct}</MenuItem>
              )}
              {dummyProducts.map((product) => (
                <MenuItem key={product} value={product}>
                  {product}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Box>

      <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
        <CustomButton
          onClick={onSearch}
          buttonSize="large"
          sx={{
            minWidth: "200px",
          }}
        >
          <SearchIcon sx={{ mr: 1 }} />
          Ara
        </CustomButton>
      </Box>
    </Card>
  );
};

export default SearchForm;