from flask import Flask
from flask_cors import CORS
from auth import auth
from p1 import p1
from p2 import p2
from p3 import p3
from p4 import p4

from database.database import Base, engine, SessionLocal
from database.models import User
from werkzeug.security import generate_password_hash
from datetime import datetime, timed<PERSON><PERSON>

def initialize_database():
    """Veritabanı tablolarını oluştur ve admin kullanıcıyı ekle (gerekirse)"""
    Base.metadata.create_all(bind=engine)
    db = SessionLocal()

    if not db.query(User).filter(User.username == "admin").first():
        expiration_date = datetime.utcnow() + timedelta(days=90)
        admin = User(
            username="admin",
            password_hash=generate_password_hash("PeraPole2025!"),
            account_expiration=expiration_date,
            allowed_endpoints=["p1", "p2", "p3", "p4"]  # 🔐 tüm yetkiler verildi
        )
        db.add(admin)
        db.commit()
        print("Ad<PERSON> kullanıcısı oluşturuldu.")
    else:
        print("Admin zaten kayıtlı.")

    db.close()

# Uygulama başlatılıyor
app = Flask(__name__)
CORS(app)

# Otomatik veritabanı kontrolü ve ilk kullanıcı oluşturma
initialize_database()

# Blueprint'leri kaydet
app.register_blueprint(auth, url_prefix='/auth')
app.register_blueprint(p1, url_prefix='/p1')
app.register_blueprint(p2, url_prefix='/p2')
app.register_blueprint(p3, url_prefix='/p3')
app.register_blueprint(p4, url_prefix='/p4')

if __name__ == '__main__':
    app.run()
