import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import MainProjectSelection from "@/components/MainProjectSelection";

const Root = styled("div")(({ theme }) => ({
  "& > .logo-icon": {
    transition: theme.transitions.create(["width", "height"], {
      duration: theme.transitions.duration.shortest,
      easing: theme.transitions.easing.easeInOut,
    }),
  },
  "& > .badge": {
    transition: theme.transitions.create("opacity", {
      duration: theme.transitions.duration.shortest,
      easing: theme.transitions.easing.easeInOut,
    }),
  },
}));

/**
 * The logo component.
 */
function Logo() {
  return (
    <Root className="flex flex-1 items-center space-x-12">
      <div className="flex flex-1 items-center px-10">
        <img
          className="logo-icon h-48 w-48"
          src="/assets/images/logo/perapole.png"
          alt="logo"
        />
        <div className="logo-text flex flex-auto gap-2">
          <Typography className="text-2xl text-[#24C1D0] tracking-light font-semibold leading-none">
            PERA<span className="text-[#F06032]">POLE</span>
          </Typography>
        </div>
      </div>

      {/* <MainProjectSelection /> */}
    </Root>
  );
}

export default Logo;
