"use client";
import React, { useEffect, useState, useMemo } from "react";
import { Box, Container, Typography, useTheme, Tabs, Tab } from "@mui/material";
import countryList from "react-select-country-list";
import { Country, State, City } from "country-state-city";
import SearchForm from "@/components/p2/SearchForm";
import ResultsTable from "@/components/p2/ResultsTable";
import FuseLoading from "@fuse/core/FuseLoading";
import axios from "axios";
import { useSearchParams } from "next/navigation";
// Yeni eklenen: Harita sekmesi için component
import MapSearchTab from "@/components/p2/MapSearchTab";

interface Company {
  name: string;
  address: string;
  phone: string;
  website?: string;
  district: string;
  city: string;
  country: string;
  category: string;
}

const dummyProducts = [
  "Elektronik Ürünler",
  "Beyaz Eşya",
  "Mobilya",
  "Tekstil",
  "Otomotiv Parçaları",
];

const P2Container = () => {
  const theme = useTheme();
  const countries = useMemo(() => countryList().getData(), []);
  const searchParams = useSearchParams();
  const initialKeyword = decodeURIComponent(searchParams.get("product") || "");
  const [disableProductSelect, setDisableProductSelect] = useState(false);

  const [tabValue, setTabValue] = useState(0); // 0 = Klasik arama, 1 = Harita arama

  useEffect(() => {
    if (initialKeyword) {
      setSelectedProduct(initialKeyword);
      setDisableProductSelect(true);
    }
  }, [initialKeyword]);

  const countryCodeMap = useMemo(() => {
    const map = new Map();
    countries.forEach((country) => {
      map.set(country.label, country.value);
    });
    return map;
  }, [countries]);

  const [selectedCountry, setSelectedCountry] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedProduct, setSelectedProduct] = useState("");
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);

  const cities = useMemo(() => {
    if (!selectedCountry) return [];
    const countryCode = countryCodeMap.get(selectedCountry);
    if (!countryCode) return [];

    const states = State.getStatesOfCountry(countryCode);
    return states.map(state => ({
      value: state.isoCode,
      label: state.name
    }));
  }, [selectedCountry, countryCodeMap]);

  const districts = useMemo(() => {
    if (!selectedCountry || !selectedCity) return [];
    const countryCode = countryCodeMap.get(selectedCountry);
    if (!countryCode) return [];

    const cities = City.getCitiesOfState(countryCode, selectedCity);
    return cities.map(city => ({
      value: city.name,
      label: city.name
    }));
  }, [selectedCountry, selectedCity, countryCodeMap]);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('jwt_token');
      const countryCode = countryCodeMap.get(selectedCountry);
      const selectedCityData = cities.find(city => city.value === selectedCity);
      const selectedDistrictData = districts.find(district => district.value === selectedDistrict);

      const payload = {
        searchText: selectedProduct,
        country: countryCode,
        state: selectedCityData?.label || selectedCity,
        city: selectedDistrictData?.label || selectedDistrict,
      };

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_BASE_URL}/p2/get-suppliers`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      setCompanies(response.data);
      setShowResults(true);
    } catch (error: any) {
      if (error.response?.status === 401) {
        alert("🔐 Oturum süreniz doldu. Lütfen tekrar giriş yapın.");
      } else if (error.response?.status === 403) {
        alert("⛔ Bu alana erişim yetkiniz yok. Lütfen sistem yöneticinizle iletişime geçin.");
      } else {
        console.error("❌ Beklenmeyen hata:", error);
      }
    } finally {
      setLoading(false);
    }
  };


  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box sx={{
        width: "100%",
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}>
        <FuseLoading />
      </Box>
    );
  }

  return (
    <Box sx={{
      p: { xs: 2, md: 4 },
      minHeight: "100vh",
      background: theme.palette.mode === "dark"
        ? `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.paper} 100%)`
        : "linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%)",
    }}>
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 800, color: "text.primary", mb: 1 }}>
            İşletme Arama
          </Typography>
          <Typography variant="body1" sx={{ color: "text.secondary" }}>
            Kriterlere göre işletme arayın veya haritadan seçim yapın
          </Typography>
        </Box>

        {/* TABLAR */}
        <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 4 }}>
          <Tab label="Detaylı Arama (Ülke/Şehir/İlçe)" />
          <Tab label="Haritadan Arama" />
        </Tabs>

        {/* 1. TAB */}
        {tabValue === 0 && (
          <>
            <SearchForm
              countries={countries}
              selectedCountry={selectedCountry}
              selectedCity={selectedCity}
              selectedDistrict={selectedDistrict}
              selectedProduct={selectedProduct}
              cities={cities}
              districts={districts}
              dummyProducts={dummyProducts}
              onCountryChange={setSelectedCountry}
              onCityChange={setSelectedCity}
              onDistrictChange={setSelectedDistrict}
              onProductChange={setSelectedProduct}
              onSearch={handleSearch}
              disableProductSelect={!!initialKeyword}
            />

            {showResults && companies && <ResultsTable companies={companies} />}
          </>
        )}

        {/* 2. TAB */}
        {tabValue === 1 && (
          <MapSearchTab />
        )}
      </Container>
    </Box>
  );
};

export default P2Container;
