import React from "react";
import { Button, ButtonProps } from "@mui/material";

interface CustomButtonProps extends ButtonProps {
  customWidth?: string;
  buttonSize?: "default" | "large";
}

const CustomButton: React.FC<CustomButtonProps> = ({
  children,
  customWidth,
  buttonSize = "default",
  ...props
}) => {
  const isLarge = buttonSize === "large";

  return (
    <Button
      variant="contained"
      {...props}
      sx={{
        minWidth: customWidth || (isLarge ? "250px" : "200px"),
        py: isLarge ? 2.5 : 1.5,
        px: 4,
        borderRadius: "16px",
        backgroundColor: "#64748B",
        "&:hover": {
          backgroundColor: "#475569",
          transform: "translateY(-2px)",
          boxShadow: "0 12px 30px rgba(100, 116, 139, 0.4)",
        },
        color: "white",
        fontSize: isLarge ? "1.25rem" : "1rem",
        fontWeight: 600,
        textTransform: "none",
        boxShadow: "0 8px 24px rgba(100, 116, 139, 0.3)",
        transition: "all 0.3s ease",
        "&:disabled": {
          backgroundColor: "#E2E8F0",
          color: "#94A3B8",
        },
        ...props.sx,
      }}
    >
      {children}
    </Button>
  );
};

export default CustomButton;
