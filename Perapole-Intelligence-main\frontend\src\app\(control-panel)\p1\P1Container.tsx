"use client";
import React, { useState } from "react";
import { Box, Container, useTheme } from "@mui/material";
import Grid from "@mui/material/Grid";
import SearchInput from "@/components/p1/SearchInput";
import AttributeCard from "@/components/p1/AttributeCard";
import CustomButton from "@/components/p1/CustomButton";
import ProductCard from "@/components/p1/ProductCard";
import FuseLoading from "@fuse/core/FuseLoading";
import axios from "axios";

interface ProductResponse {
  attributes: Record<string, string[]>;
  images: string[];
  kusursuz_urun_adi: string;
  "tahmini fiyat": string;
  "urun-adı": string;
}

const P1Container = () => {
  const theme = useTheme();
  const [attributes, setAttributes] = useState<Record<string, string[]>>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAttributes, setSelectedAttributes] = useState<
    Record<string, string[]>
  >({});
  const [showProductCard, setShowProductCard] = useState(false);
  const [loading, setLoading] = useState(false);
  const [productImages, setProductImages] = useState<string[]>([]);
  const [productName, setProductName] = useState("");
  const [estimatedPrice, setEstimatedPrice] = useState("");

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('jwt_token');

      const response = await axios.post<ProductResponse>(
        `${process.env.NEXT_PUBLIC_BASE_URL}/p1/get-product-details`,
        {
          productName: searchTerm,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      setAttributes(response.data.attributes);
      setProductImages(response.data.images);
      setProductName(response.data.kusursuz_urun_adi);
      setEstimatedPrice(response.data["tahmini fiyat"]);
      setSelectedAttributes({});
    } catch (error: any) {
      if (error.response?.status === 401) {
        alert("🔐 Oturum süreniz doldu. Lütfen tekrar giriş yapın.");
      } else if (error.response?.status === 403) {
        alert("⛔ Bu alana erişim yetkiniz yok. Lütfen sistem yöneticinizle iletişime geçin.");
      } else {
        console.error("❌ Beklenmeyen hata:", error);
      }
    } finally {
      setLoading(false);
    }
  };


  const handleChange = (attrName: string, values: string[]) => {
    setSelectedAttributes((prev) => ({
      ...prev,
      [attrName]: values,
    }));
  };

  const handleDeleteAttribute = (attrName: string) => {
    const newAttributes = { ...attributes };
    delete newAttributes[attrName];
    setAttributes(newAttributes);

    const newSelectedAttributes = { ...selectedAttributes };
    delete newSelectedAttributes[attrName];
    setSelectedAttributes(newSelectedAttributes);
  };

  const handleValueDelete = (attrName: string, value: string) => {
    const newValues =
      selectedAttributes[attrName]?.filter((v) => v !== value) || [];
    handleChange(attrName, newValues);

    const newAttrValues = attributes[attrName].filter((v) => v !== value);
    setAttributes((prev) => ({
      ...prev,
      [attrName]: newAttrValues,
    }));
  };

  const handleCreateCard = () => {
    const filteredAttributes: Record<string, string[]> = {};
    Object.entries(selectedAttributes).forEach(([key, values]) => {
      if (values.length > 0) {
        filteredAttributes[key] = values;
      }
    });

    setShowProductCard(true);
  };

  const productData = {
    title: productName || "Özel Tasarım Kalem",
    attributes: Object.entries(selectedAttributes).reduce(
      (acc, [key, values]) => {
        if (values.length > 0) {
          acc[key] = values;
        }
        return acc;
      },
      {} as Record<string, string[]>
    ),
    images: productImages,
    estimatedPrice: estimatedPrice,
  };

  if (loading) {
    return (
      <Box
        sx={{
          width: "100%",
          height: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <FuseLoading />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        p: { xs: 2, md: 4 },
        minHeight: "100vh",
        background:
          theme.palette.mode === "dark"
            ? `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.paper} 100%)`
            : "linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%)",
      }}
    >
      <Container maxWidth="xl">
        {!showProductCard ? (
          <>
            <SearchInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onSearch={handleSearch}
            />

            <Grid container spacing={3}>
              {Object.entries(attributes).map(([attrName, values]) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={attrName}>
                  <AttributeCard
                    attrName={attrName}
                    values={values}
                    selectedValues={selectedAttributes[attrName] || []}
                    onDelete={handleDeleteAttribute}
                    onChange={handleChange}
                    onValueDelete={handleValueDelete}
                  />
                </Grid>
              ))}
            </Grid>

            <Box sx={{ mt: 6, mb: 2, textAlign: "center" }}>
              {Object.keys(attributes).length > 0 && (
                <CustomButton
                  buttonSize="large"
                  onClick={handleCreateCard}
                  sx={{
                    fontSize: "1.4rem",
                    letterSpacing: "0.5px",
                  }}
                >
                  Kart Oluştur
                </CustomButton>
              )}
            </Box>
          </>
        ) : (
          <ProductCard
            productData={productData}
            onBack={() => setShowProductCard(false)}
          />
        )}
      </Container>
    </Box>
  );
};

export default P1Container;
