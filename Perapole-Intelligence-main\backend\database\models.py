from sqlalchemy import Column, Integer, String, DateTime
from database.database import Base
from datetime import datetime, timedelta
from sqlalchemy.dialects.postgresql import JSON

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=False)
    account_expiration = Column(DateTime, nullable=False, default=lambda: datetime.utcnow() + timedelta(days=30))
    allowed_endpoints = Column(JSON, nullable=False)  # örnek: ["p1", "p2"]
