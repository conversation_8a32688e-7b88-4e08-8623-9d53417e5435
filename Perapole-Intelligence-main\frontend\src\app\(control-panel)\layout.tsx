'use client';

import { useEffect } from 'react';
import MainLayout from 'src/components/MainLayout';
import AuthGuardRedirect from '@auth/AuthGuardRedirect';
import { setGlobalHeaders } from '@/utils/apiFetch';

function Layout({ children }) {
	useEffect(() => {
		// Uygulama yüklendiğinde token varsa global header olarak ayarla
		const token = localStorage.getItem('jwt_token');
		if (token) {
			setGlobalHeaders({
				Authorization: `Bearer ${token}`
			});
		}
	}, []);

	return (
		<AuthGuardRedirect auth={['admin']}>
			<MainLayout>{children}</MainLayout>
		</AuthGuardRedirect>
	);
}

export default Layout;
