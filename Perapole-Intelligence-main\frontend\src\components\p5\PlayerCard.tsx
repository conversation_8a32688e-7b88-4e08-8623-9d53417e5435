import React from "react";
import { Box, Typography, Card, Avatar, useTheme } from "@mui/material";

interface PlayerInfo {
  name: string;
  fullName: string;
  birthDate: string;
  height?: string;
  weight?: string;
  nationality?: string;
  position?: string;
  stats: any[];
}

interface PlayerCardProps {
  player: PlayerInfo;
}

export const PlayerCard: React.FC<PlayerCardProps> = ({ player }) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        mb: 3,
        borderRadius: "16px",
        overflow: "hidden",
        boxShadow: theme.shadows[3],
        backgroundColor: "background.paper",
      }}
    >
      <Box
        sx={{
          display: "flex",
          gap: 3,
          p: 3,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Avatar
          src="/messi.jpg"
          sx={{ width: 100, height: 100, borderRadius: "12px" }}
        />
        <Box sx={{ flex: 1 }}>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, mb: 1, color: "text.primary" }}
          >
            {player.name}
          </Typography>
          <Typography variant="body2" sx={{ color: "text.secondary", mb: 2 }}>
            {player.fullName}
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              gap: { xs: 2, md: 4 },
            }}
          >
            <Box>
              <Typography
                component="span"
                variant="caption"
                sx={{ color: "text.secondary", mr: 1 }}
              >
                Doğum Tarihi:
              </Typography>
              <Typography component="span" variant="body2" color="text.primary">
                {player.birthDate}
              </Typography>
            </Box>
            {player.height && (
              <Box>
                <Typography
                  component="span"
                  variant="caption"
                  sx={{ color: "text.secondary", mr: 1 }}
                >
                  Boy:
                </Typography>
                <Typography
                  component="span"
                  variant="body2"
                  color="text.primary"
                >
                  {player.height}
                </Typography>
              </Box>
            )}
            {player.weight && (
              <Box>
                <Typography
                  component="span"
                  variant="caption"
                  sx={{ color: "text.secondary", mr: 1 }}
                >
                  Kilo:
                </Typography>
                <Typography
                  component="span"
                  variant="body2"
                  color="text.primary"
                >
                  {player.weight}
                </Typography>
              </Box>
            )}
            {player.position && (
              <Box>
                <Typography
                  component="span"
                  variant="caption"
                  sx={{ color: "text.secondary", mr: 1 }}
                >
                  Pozisyon:
                </Typography>
                <Typography
                  component="span"
                  variant="body2"
                  color="text.primary"
                >
                  {player.position}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Card>
  );
};
