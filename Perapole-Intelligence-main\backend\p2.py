import time
import requests
import asyncio
import aiohttp
from flask import Blueprint, jsonify, request
from deep_translator import GoogleTranslator
import google.generativeai as genai
from collections import OrderedDict
import math
from auth import require_permission

p2 = Blueprint('/p2', __name__)
GOOGLE_API_KEY = "AIzaSyARlEfomkKKioDlZCKddMAz590HenzSDrI"

@p2.route('/get-suppliers-by-map', methods=['POST'])
@require_permission('p2')
def get_suppliers_by_map():
    """
    Kullanıcı harita üzerinden merkez koordinat ve yarıçap vererek arama yapıyor.
    Ürün kategorisi de gönderiliyor.
    """
    data = request.json
    center_latitude = data.get('centerLatitude')
    center_longitude = data.get('centerLongitude')
    radius = data.get('radius')
    search_text = data.get('searchText', '')
    country_code = data.get('country', '')

    if not center_latitude or not center_longitude or not radius or not search_text:
        return jsonify({"error": "Eksik bilgi gönderildi"}), 400

    # Önce arama için kullanılacak keyword'ü üretelim
    keyword = generate_location_based_keyword(search_text, country_code)

    # Şimdi bu bilgilerle kombinasyonu başlatalım
    combined_results = asyncio.run(
        get_combined_results(keyword, center_latitude, center_longitude, radius, country_code)
    )

    return jsonify(combined_results)


@p2.route('/generate-keyword', methods=['POST'])
@require_permission('p2')
def generate_keyword():
    data = request.get_json()
    product_title = data.get('productTitle')
    attributes = data.get('attributes')

    prompt = f"""
    You are a B2B supplier assistant. A user is looking for a supplier for the product: "{product_title}".
    Product details:
    """
    for key, values in attributes.items():
        prompt += f"- {key}: {', '.join(values)}\n"

    prompt += """
    Your task is to generate a short (2–5 word) keyword **in English** that can be used in a **Google Maps search** to find businesses that sell or distribute this kind of product.

    ⚠️ Important:
    - Do NOT return the product name itself. Return the kind of **business** that sells this product.
    - If the product is "fountain pen", return something like **"stationery shop"**, NOT "fountain pen distributor".

    🎯 Return ONLY the keyword. No explanation or markdown.
    """

    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel('gemini-1.5-flash')
    response = model.generate_content(prompt)
    keyword = response.text.strip()
    return jsonify({"keyword": keyword})


@p2.route('/get-suppliers', methods=['POST'])
@require_permission('p2')
def get_suppliers():
    data = request.json
    search_text = data.get('searchText', '')
    selected_country = data.get('country', '')
    selected_state = data.get('state', '')
    selected_city = data.get('city', '')

    keyword = generate_location_based_keyword(search_text, selected_country)
    radius = generate_radius_for_location(selected_city, selected_state, selected_country)
    # Eğer city ve state yoksa, ülkenin ekonomik merkezini al
    if not selected_city and not selected_state:
        selected_city = get_major_economic_city(selected_country)

    latitude, longitude = get_lat_lon_google(selected_country, selected_state, selected_city)
    # if not (latitude and longitude):
    #     return jsonify([]), 400

    combined_results = asyncio.run(get_combined_results(keyword, latitude, longitude, radius, selected_country))

    return jsonify(combined_results)


def get_lat_lon_google(country, state=None, city=None):
    # Diğer durumlarda Geocoding API'ye başvur
    address_parts = []
    if city:
        address_parts.append(city)
    if state:
        address_parts.append(state)
    address_parts.append(country)
    address = ", ".join(address_parts)

    geocode_url = "https://maps.googleapis.com/maps/api/geocode/json"
    params = {
        "address": address,
        "key": GOOGLE_API_KEY
    }

    response = requests.get(geocode_url, params=params)
    if response.status_code == 200:
        results = response.json().get("results", [])
        if results:
            location = results[0]["geometry"]["location"]
            return location["lat"], location["lng"]

    return None, None


async def fetch_old_places(session, query, latitude, longitude, radius,country):
    search_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    detail_url = "https://maps.googleapis.com/maps/api/place/details/json"

    params = {
        "key": GOOGLE_API_KEY,
        "keyword": query
    }

    if latitude and longitude:
        params["location"] = f"{latitude},{longitude}"
        params["radius"] = radius


    all_places = []

    while True:
        async with session.get(search_url, params=params) as response:
            if response.status != 200:
                break
            data = await response.json()
            all_places.extend(data.get("results", []))
            next_token = data.get("next_page_token")
            if not next_token:
                break
            params["pagetoken"] = next_token
            await asyncio.sleep(2)

    detailed_results = []
    for place in all_places:
        place_id = place.get("place_id")
        if not place_id:
            continue

        detail_params = {
            "key": GOOGLE_API_KEY,
            "place_id": place_id,
            "fields": "name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,types,geometry"
        }

        async with session.get(detail_url, params=detail_params) as detail_response:
            if detail_response.status != 200:
                continue
            detail_data = await detail_response.json()
            result = detail_data.get("result", {})

            if not result.get("website"):
                continue  # ❗ Web sitesi yoksa ekleme

            detailed_results.append({
                "name": result.get("name"),
                "address": result.get("formatted_address"),
                "website": result.get("website"),
                "rating": result.get("rating"),
                "reviews": result.get("user_ratings_total"),
                "types": result.get("types"),
                "geometry": result.get("geometry"),
                "google_place_id": place_id,
                "country": country,
            })

    return detailed_results


async def fetch_new_places(session, query, latitude, longitude, radius,country):
    url = "https://places.googleapis.com/v1/places:searchText"
    headers = {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": GOOGLE_API_KEY,
        "X-Goog-FieldMask": (
            "places.displayName.text,"
            "places.formattedAddress,"
            "places.websiteUri,"
            "places.types,"
            "places.rating,"
            "places.userRatingCount,"
            "places.location"
        )
    }

    payload = {
        "textQuery": query,
        "pageSize": 30
    }

    if latitude and longitude:
        payload["locationBias"] = {
            "circle": {
                "center": {
                    "latitude": latitude,
                    "longitude": longitude
                },
                "radius": float(radius)
            }
        }


    results = []
    async with session.post(url, headers=headers, json=payload) as response:
        if response.status != 200:
            return []
        data = await response.json()
        for p in data.get("places", []):
            website = p.get("websiteUri")
            if not website:
                continue  # ❗ Web sitesi olmayanları atla

            results.append({
                "name": p.get("displayName", {}).get("text", "N/A"),
                "address": p.get("formattedAddress", "N/A"),
                "website": website,
                "rating": p.get("rating", "unknown"),
                "reviews": p.get("userRatingCount", "unknown"),
                "types": p.get("types", []),
                "geometry": {
                    "location": p.get("location", {})
                },  # Yeni API telefon bilgisini doğrudan sağlamaz
                "google_place_id": "unknown",
                "country": country  # Yeni API'de yer almıyor
            })

    return results

async def get_combined_results(query, latitude, longitude, radius, country):
    async with aiohttp.ClientSession() as session:
        task1 = asyncio.create_task(fetch_old_places(session, query, latitude, longitude, radius, country))
        task2 = asyncio.create_task(fetch_new_places(session, query, latitude, longitude, radius, country))

        done, _ = await asyncio.wait(
            [task1, task2],
            return_when=asyncio.ALL_COMPLETED
        )

        results = []
        for task in done:
            results.extend(task.result())

        # Yinelenenleri filtrele
        unique = {(r["name"], r["address"]): r for r in results}
        filtered = list(unique.values())

        # 🔥 Kullanıcının seçtiği merkeze göre en yakından uzağa sırala
        sorted_results = sorted(
            filtered,
            key=lambda x: haversine_distance(
                latitude,
                longitude,
                x["geometry"]["location"].get("lat", 0),
                x["geometry"]["location"].get("lng", 0)
            )
        )

        return sorted_results

def get_major_economic_city(country_code):
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")

    prompt = f"""
    You are a geographic intelligence assistant.

    A user has only specified a country: "{country_code}". Your task is to return the **most economically significant city** in this country.

    🚫 Do not include the country name or any other explanation.
    🚫 Do not return multiple cities, regions, or states.
    ✅ Return exactly **one single city name** (e.g., "Istanbul").

    Output format:
    Just the city name, nothing else.
    """

    response = model.generate_content(prompt)
    return response.text.strip()

def generate_radius_for_location(city, state, country):
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")

    prompt = f"""
    You are helping to determine the ideal radius (in meters) for a business search on Google Maps.

    A B2B supplier search is being conducted in:
    - City: {city}
    - State/Region: {state}
    - Country: {country}

    The goal is to find businesses or vendors that supply products within a reasonable delivery or commuting distance.

    ⚠️ Important:
    - If both the city and state/region are missing, the search must be **country-wide**.
    - In that case, you must provide a **wider radius** appropriate for nationwide supplier discovery.
    - Do not return empty or vague results. Return only a realistic, concrete number in meters.

    🎯 Output format:
    Just the number (e.g., 50000), no units, no explanation.
    """

    response = model.generate_content(prompt)
    return int(''.join(filter(str.isdigit, response.text.strip())))

def generate_location_based_keyword(search_text, country_code):
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel("gemini-1.5-flash")

    prompt = f"""
    You are a B2B supplier search assistant. A user from "{country_code}" is searching for suppliers of "{search_text}".

    Please generate a short and simple keyword (2–5 words) that would be effective to use in a **Google Maps search** for businesses that sell or distribute this product.

    The keyword should be in the **official language** of the user's country ("{country_code}") and include terms like "supplier", "store", "distributor", or "vendor" in that language.

    Return only the keyword. Do not include any explanations.
    """

    response = model.generate_content(prompt)
    return response.text.strip()

import math

def haversine_distance(lat1, lon1, lat2, lon2):
    R = 6371000  # Dünya yarıçapı metre cinsinden
    phi1 = math.radians(lat1)
    phi2 = math.radians(lat2)
    delta_phi = math.radians(lat2 - lat1)
    delta_lambda = math.radians(lon2 - lon1)
    
    a = math.sin(delta_phi / 2.0) ** 2 + \
        math.cos(phi1) * math.cos(phi2) * \
        math.sin(delta_lambda / 2.0) ** 2
    
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return R * c  # metre cinsinden mesafe
