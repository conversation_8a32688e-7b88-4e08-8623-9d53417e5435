import React from "react";
import { TextField, Paper, Box, useTheme } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import CustomButton from "./CustomButton";

interface SearchInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSearch: () => void;
}

const SearchInput: React.FC<SearchInputProps> = ({
  value,
  onChange,
  onSearch,
}) => {
  const theme = useTheme();

  return (
    <Box display="flex" justifyContent="center" sx={{ mb: 5 }}>
      <Paper
        elevation={0}
        sx={{
          p: 1.5,
          borderRadius: 4,
          display: "flex",
          gap: 1,
          background: theme.palette.background.paper,
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 2px 20px rgba(0,0,0,0.2)"
              : "0 2px 20px rgba(0,0,0,0.05)",
          maxWidth: { xs: "100%", sm: "600px" },
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <TextField
          placeholder="Ürün aramak için yazın..."
          value={value}
          onChange={onChange}
          InputProps={{
            startAdornment: (
              <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
            ),
          }}
          sx={{
            flex: 1,
            "& .MuiOutlinedInput-root": {
              backgroundColor: "transparent",
              "& fieldset": {
                display: "none",
              },
              "& input": {
                color: "text.primary",
                "&::placeholder": {
                  color: "text.secondary",
                  opacity: 0.7,
                },
              },
            },
          }}
        />
        <CustomButton
          onClick={onSearch}
          customWidth="auto"
          sx={{
            borderRadius: 3,
            px: { xs: 3, sm: 4 },
          }}
        >
          Ara
        </CustomButton>
      </Paper>
    </Box>
  );
};

export default SearchInput;
