import React from "react";
import {
  Box,
  Card,
  Typography,
  FormControl,
  Select,
  MenuItem,
  useTheme,
} from "@mui/material";
import { ChatMessage } from "./ChatMessage";
import { ChatInput } from "./ChatInput";
import FuseLoading from "@fuse/core/FuseLoading";

interface Message {
  id: number;
  text: string;
  translatedText: string;
  timestamp: Date;
  sender: "customer" | "supplier";
}

interface ChatWindowProps {
  title: string;
  language: string;
  onLanguageChange: (value: string) => void;
  messages: Message[];
  messageText: string;
  onMessageChange: (text: string) => void;
  onSend: () => void;
  viewingLanguage: string;
  role: "customer" | "supplier";
  languages: { code: string; name: string }[];
  loading: boolean;
}

export const ChatWindow: React.FC<ChatWindowProps> = ({
  title,
  language,
  onLanguageChange,
  messages,
  messageText,
  onMessageChange,
  onSend,
  viewingLanguage,
  role,
  languages,
  loading,
}) => {
  const theme = useTheme();

  const getMessageText = (message: Message) => {
    const shouldShowTranslation =
      // Eğer bu müşteri penceresi ve mesaj tedarikçiden geldiyse
      (role === "customer" && message.sender === "supplier") ||
      // VEYA bu tedarikçi penceresi ve mesaj müşteriden geldiyse
      (role === "supplier" && message.sender === "customer");

    // Çevrilmiş mesajı göstermemiz gerekiyorsa translatedText'i,
    // değilse orijinal metni göster
    return shouldShowTranslation ? message.translatedText : message.text;
  };

  return (
    <Card
      sx={{
        height: "100%",
        borderRadius: "24px",
        background:
          theme.palette.mode === "dark"
            ? "rgba(0,0,0,0.6)"
            : "rgba(255,255,255,0.9)",
        backdropFilter: "blur(10px)",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 4px 24px rgba(0,0,0,0.2)"
            : "0 4px 24px rgba(0,0,0,0.06)",
        display: "flex",
        flexDirection: "column",
        position: "relative",
      }}
    >
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(0,0,0,0.3)",
            zIndex: 1000,
            borderRadius: "24px",
          }}
        >
          <FuseLoading />
        </Box>
      )}

      <Box
        sx={{
          p: 3,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography
          variant="h6"
          sx={{ fontWeight: 700, color: "text.primary", mb: 2 }}
        >
          {title}
        </Typography>
        <FormControl fullWidth>
          <Select
            value={language}
            onChange={(e) => onLanguageChange(e.target.value)}
            sx={{
              borderRadius: "16px",
              backgroundColor: theme.palette.background.paper,
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor:
                  theme.palette.mode === "dark"
                    ? "rgba(255,255,255,0.1)"
                    : "rgba(0,0,0,0.08)",
                borderRadius: "16px",
              },
              "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: "text.secondary",
              },
            }}
          >
            {languages.map((lang) => (
              <MenuItem key={lang.code} value={lang.code}>
                {lang.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Box
        sx={{
          p: 3,
          flexGrow: 1,
          backgroundColor: theme.palette.background.default,
          overflowY: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 2,
        }}
      >
        {messages.map((msg) => (
          <ChatMessage
            key={msg.id}
            text={getMessageText(msg)}
            timestamp={msg.timestamp}
            isOwnMessage={msg.sender === role}
          />
        ))}
      </Box>

      <ChatInput
        value={messageText}
        onChange={onMessageChange}
        onSend={onSend}
      />
    </Card>
  );
};
