# backend/ssm.py

from deep_translator import GoogleTranslator
from flask import Blueprint, jsonify, request
from auth import require_permission

p3 = Blueprint('p3', __name__)

@p3.route('/send-message', methods=['POST'])
@require_permission('p3')
def send_message():
    message = request.json['message']
    source_language = request.json.get('sourceLanguage')
    target_language = request.json.get('targetLanguage')

    translated_message = GoogleTranslator(source=source_language, target=target_language).translate(message)

    return jsonify(response=translated_message)
