import React from "react";
import { Box, TextField, useTheme } from "@mui/material";
import CustomButton from "../p1/CustomButton";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSend,
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        p: 3,
        borderTop: `1px solid ${theme.palette.divider}`,
        display: "flex",
        gap: 2,
        alignItems: "center",
      }}
    >
      <TextField
        fullWidth
        placeholder="Mesaj yazın..."
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            onSend();
          }
        }}
        sx={{
          "& .MuiOutlinedInput-root": {
            borderRadius: "12px",
            backgroundColor: theme.palette.background.paper,
            height: "44px",
            "& fieldset": {
              borderColor: theme.palette.divider,
            },
            "&:hover fieldset": {
              borderColor: "text.secondary",
            },
            "& textarea": {
              padding: "10px 14px",
              cursor: "text",
              color: "text.primary",
            },
          },
          "& .MuiInputBase-input": {
            cursor: "text !important",
            caretColor: "text.secondary",
          },
        }}
      />
      <CustomButton
        onClick={onSend}
        sx={{
          minWidth: "auto",
          px: 3,
          height: "44px",
        }}
      >
        Gönder
      </CustomButton>
    </Box>
  );
};
