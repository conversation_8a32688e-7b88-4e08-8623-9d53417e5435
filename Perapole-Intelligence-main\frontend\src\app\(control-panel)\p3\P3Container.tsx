"use client";
import React, { useState } from "react";
import { Box, Container, Typography, Grid, useTheme } from "@mui/material";
import { ChatWindow } from "@/components/p3/ChatWindow";
import axios from "axios";
import { useSearchParams } from "next/navigation";
import { languageNameToCodeMap } from "@/utils/languageMap"; // 🔑

interface Message {
  id: number;
  text: string;
  translatedText: string;
  timestamp: Date;
  sender: "customer" | "supplier";
}

interface TranslationResponse {
  response: string;
}

// 🔄 languages listesini languageNameToCodeMap üzerinden üret
const languages = Object.entries(languageNameToCodeMap).map(([name, code]) => ({
  code,
  name: name.charAt(0).toUpperCase() + name.slice(1), // örn. "turkish" -> "Turkish"
}));

const P3Container = () => {
  const theme = useTheme();
  const searchParams = useSearchParams();

  const customerLangParam = searchParams.get("customerLang") || "turkish";
  const supplierLangParam = searchParams.get("supplierLang") || "english";

  const initialCustomerLang = languageNameToCodeMap[customerLangParam.toLowerCase()] || "en";
  const initialSupplierLang = languageNameToCodeMap[supplierLangParam.toLowerCase()] || "en";

  const [customerLanguage, setCustomerLanguage] = useState(initialCustomerLang);
  const [supplierLanguage, setSupplierLanguage] = useState(initialSupplierLang);
  const [customerMessage, setCustomerMessage] = useState("");
  const [supplierMessage, setSupplierMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);

  const handleCustomerSend = async () => {
    if (customerMessage.trim()) {
      setLoading(true);
      try {
        const token = localStorage.getItem("jwt_token");
        console.log("customer token", token);

        const {
          data: { response: translatedText },
        } = await axios.post<TranslationResponse>(
          `${process.env.NEXT_PUBLIC_BASE_URL}/p3/send-message`,
          {
            message: customerMessage,
            sourceLanguage: customerLanguage,
            targetLanguage: supplierLanguage,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            text: customerMessage,
            translatedText,
            timestamp: new Date(),
            sender: "customer",
          },
        ]);

        setCustomerMessage("");
      } catch (error: any) {
        if (error.response?.status === 401) {
          alert("🔐 Oturum süreniz doldu. Lütfen tekrar giriş yapın.");
        } else if (error.response?.status === 403) {
          alert("⛔ Bu alana erişim yetkiniz yok. Lütfen sistem yöneticinizle iletişime geçin.");
        } else {
          console.error("❌ Beklenmeyen hata:", error);
        }
      } finally {
        setLoading(false);
      }
    }
  };


  const handleSupplierSend = async () => {
    if (supplierMessage.trim()) {
      setLoading(true);
      try {
        const token = localStorage.getItem("jwt_token");
        console.log("token", token);
        const {
          data: { response: translatedText },
        } = await axios.post<TranslationResponse>(
          `${process.env.NEXT_PUBLIC_BASE_URL}/p3/send-message`,
          {
            message: supplierMessage,
            sourceLanguage: supplierLanguage,
            targetLanguage: customerLanguage,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        setMessages((prev) => [
          ...prev,
          {
            id: Date.now(),
            text: supplierMessage,
            translatedText,
            timestamp: new Date(),
            sender: "supplier",
          },
        ]);

        setSupplierMessage("");
      } catch (error: any) {
        if (error.response?.status === 401) {
          alert("🔐 Oturum süreniz doldu. Lütfen tekrar giriş yapın.");
        } else if (error.response?.status === 403) {
          alert("⛔ Bu alana erişim yetkiniz yok. Lütfen sistem yöneticinizle iletişime geçin.");
        } else {
          console.error("❌ Beklenmeyen hata:", error);
        }
      } finally {
        setLoading(false);
      }
    }
  };


  return (
    <Box
      sx={{
        p: { xs: 2, md: 4 },
        minHeight: "100vh",
        background:
          theme.palette.mode === "dark"
            ? `linear-gradient(135deg, ${theme.palette.background.default} 0%, ${theme.palette.background.paper} 100%)`
            : "linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%)",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Container maxWidth="xl" sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 800, color: "text.primary", mb: 1 }}>
            Çeviri Chat
          </Typography>
          <Typography variant="body1" sx={{ color: "text.secondary" }}>
            Farklı dillerde anlık mesajlaşma
          </Typography>
        </Box>

        <Grid container spacing={4} sx={{ flex: 1 }}>
          <Grid item xs={12} md={6} sx={{ height: { xs: "50vh", md: "100%" } }}>
            <ChatWindow
              title="Müşteri"
              language={customerLanguage}
              onLanguageChange={setCustomerLanguage}
              messages={messages}
              messageText={customerMessage}
              onMessageChange={setCustomerMessage}
              onSend={handleCustomerSend}
              viewingLanguage={customerLanguage}
              role="customer"
              languages={languages}
              loading={loading}
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ height: { xs: "50vh", md: "100%" } }}>
            <ChatWindow
              title="Tedarikçi"
              language={supplierLanguage}
              onLanguageChange={setSupplierLanguage}
              messages={messages}
              messageText={supplierMessage}
              onMessageChange={setSupplierMessage}
              onSend={handleSupplierSend}
              viewingLanguage={supplierLanguage}
              role="supplier"
              languages={languages}
              loading={loading}
            />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default P3Container;
