import React from 'react';
import { List, ListItem, ListItemButton, ListItemText, ListItemAvatar, Avatar, Typography } from '@mui/material';

interface Player {
  id: number;
  name: string;
  common_name: string;
  date_of_birth: string;
  image_path: string;
}

interface PlayerListProps {
  players: Player[];
  onPlayerSelect: (playerId: number) => void;
}

export const PlayerList: React.FC<PlayerListProps> = ({ players, onPlayerSelect }) => {
  return (
    <List sx={{ width: '100%', bgcolor: 'background.paper', borderRadius: 2, mb: 3 }}>
      {players.map((player) => (
        <ListItem
          key={player.id}
          disablePadding
          divider
        >
          <ListItemButton onClick={() => onPlayerSelect(player.id)}>
            <ListItemAvatar>
              <Avatar
                src={player.image_path}
                alt={player.name}
                sx={{ width: 40, height: 40 }}
              />
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography variant="subtitle1" component="div">
                  {player.common_name || player.name}
                </Typography>
              }
              secondary={
                <Typography variant="body2" color="text.secondary">
                  Doğum Tarihi: {player.date_of_birth}
                </Typography>
              }
            />
          </ListItemButton>
        </ListItem>
      ))}
    </List>
  );
};