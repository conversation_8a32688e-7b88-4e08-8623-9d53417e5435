from flask import Blueprint, request, jsonify
import jwt
import base64
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
from datetime import datetime, timedelta
from database.database import SessionLocal
from database.models import User

auth = Blueprint('auth', __name__)

JWT_SECRET = "perapole_super_secret_key"
REGISTRATION_KEY = "perapole_register_2025"  # Güvenlik anahtarı

def create_token(username, allowed_endpoints):
    exp_time = datetime.utcnow() + timedelta(hours=24)
    return jwt.encode({
        'username': username,
        'exp': exp_time,
        'allowed_endpoints': allowed_endpoints
    }, JWT_SECRET, algorithm='HS256')


def verify_token(token):
    try:
        return jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
    except:
        return None

def parse_basic_auth(auth_header):
    try:
        encoded = auth_header.split(' ')[1]
        decoded_bytes = base64.b64decode(encoded)
        decoded_str = decoded_bytes.decode('utf-8')
        username, password = decoded_str.split(':')
        return username, password
    except Exception:
        return None, None

def require_auth(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'message': 'Token gerekli!'}), 401

        token = auth_header.split(' ')[1]
        payload = verify_token(token)
        if not payload:
            return jsonify({'message': 'Geçersiz veya süresi dolmuş token!'}), 401
        return f(*args, **kwargs)
    return decorated

@auth.route('/login', methods=['POST'])
def login():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Basic '):
        return jsonify({'message': 'Basic auth ile kullanıcı adı ve şifre gönderin!'}), 401

    username, password = parse_basic_auth(auth_header)
    if not username or not password:
        return jsonify({'message': 'Kullanıcı adı ve şifre çözümlenemedi!'}), 401

    db = SessionLocal()
    user = db.query(User).filter(User.username == username).first()

    if user and check_password_hash(user.password_hash, password):
        if user.account_expiration and user.account_expiration < datetime.utcnow():
            db.close()
            return jsonify({'message': 'Hesabınızın süresi dolmuştur.'}), 403

        token = create_token(user.username, user.allowed_endpoints)
        db.close()
        return jsonify({
            'token': token,
            'message': 'Giriş başarılı!'
        })
    
    db.close()
    return jsonify({'message': 'Geçersiz kullanıcı adı veya şifre!'}), 401

@auth.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    provided_key = data.get('registration_key')
    if provided_key != REGISTRATION_KEY:
        return jsonify({'message': 'Kayıt anahtarı geçersiz!'}), 403

    username = data.get('username')
    password = data.get('password')
    expiration_date_str = data.get('expiration_date')  # ISO format bekleniyor
    allowed_endpoints = data.get('allowed_endpoints', [])

    if not username or not password or not expiration_date_str:
        return jsonify({'message': 'Kullanıcı adı, şifre ve expiration_date gerekli!'}), 400
    
    if not isinstance(allowed_endpoints, list):
        return jsonify({'message': 'allowed_endpoints bir liste olmalıdır.'}), 400

    try:
        expiration_date = datetime.fromisoformat(expiration_date_str)
    except ValueError:
        return jsonify({'message': 'Geçersiz tarih formatı! ISO 8601 (YYYY-MM-DDTHH:MM:SS) bekleniyor.'}), 400

    db = SessionLocal()
    existing_user = db.query(User).filter(User.username == username).first()
    if existing_user:
        db.close()
        return jsonify({'message': 'Bu kullanıcı adı zaten kayıtlı!'}), 409

    hashed_password = generate_password_hash(password)
    new_user = User(username=username, password_hash=hashed_password, account_expiration=expiration_date, allowed_endpoints=allowed_endpoints)
    db.add(new_user)
    db.commit()
    db.close()

    return jsonify({'message': f"Kullanıcı '{username}' başarıyla kaydedildi!"}), 201

@auth.route('/protected')
@require_auth
def protected():
    return jsonify({'message': 'Bu gizli bir endpoint!'})

def require_permission(endpoint_name):
    def wrapper(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return jsonify({'message': 'Token gerekli!'}), 401

            token = auth_header.split(' ')[1]
            payload = verify_token(token)
            if not payload:
                return jsonify({'message': 'Geçersiz veya süresi dolmuş token!'}), 401

            allowed = payload.get("allowed_endpoints", [])
            if endpoint_name not in allowed:
                return jsonify({'message': f"{endpoint_name} erişimine yetkiniz yok!"}), 403

            return f(*args, **kwargs)
        return decorated
    return wrapper

@auth.route('/test-app-token', methods=['POST'])
def get_test_app_token():
    data = request.get_json()
    test_app_key = data.get('test_app_key')
    
    if test_app_key != "perapole_test_app_2025":  # Test uygulaması için özel bir anahtar
        return jsonify({'message': 'Geçersiz test uygulaması anahtarı!'}), 403
    
    # Test uygulaması için özel bir token oluştur (12 saat geçerli)
    exp_time = datetime.utcnow() + timedelta(hours=12)
    token = jwt.encode({
        'username': 'test_app',
        'exp': exp_time,
        'allowed_endpoints': ['p1', 'p2', 'p3', 'p4']  # Test uygulamasına tüm endpoint'lere erişim ver
    }, JWT_SECRET, algorithm='HS256')
    
    return jsonify({
        'token': token,
        'expires_in': 43200  # 12 saat (saniye cinsinden)
    })
